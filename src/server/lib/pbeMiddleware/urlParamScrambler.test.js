import { decode } from './urlParamScrambler';

describe('urlParamScrambler', () => {
  describe('decode', () => {
    describe('with message pack, base64 & url encoded input', () => {
      const encodedValue = 'gqRndWlk2gAkMjgzOWUwMTgtNjhmMi00ZTIxLTgxMmMtYjgyOWViOTE0OGZjp2V4cGlyZXPOXfwNzQ%3D%3D';

      it('returns expected object', () => {
        expect(decode(encodedValue)).toEqual({ expires: 1576799693, guid: '2839e018-68f2-4e21-812c-b829eb9148fc' });
      });
    });

    describe('with message pack & base64 encoded input', () => {
      const encodedValue = 'gqRndWlk2gAkMjgzOWUwMTgtNjhmMi00ZTIxLTgxMmMtYjgyOWViOTE0OGZjp2V4cGlyZXPOXfwNzQ==';

      it('returns expected object', () => {
        expect(decode(encodedValue)).toEqual({ expires: 1576799693, guid: '2839e018-68f2-4e21-812c-b829eb9148fc' });
      });
    });

    describe('empty string input', () => {
      it('throws a UrlParamScramblerDecodeError', () => {
        expect(() => decode('')).toThrow(`Unable to decode value: "". Original error: BUFFER_SHORTAGE`);
      });
    });

    describe('no input', () => {
      it('throws a UrlParamScramblerDecodeError', () => {
        expect(() => decode()).toThrow(`Unable to decode value: "undefined". Original error: BUFFER_SHORTAGE`);
      });
    });

    describe('number input', () => {
      it('throws a UrlParamScramblerDecodeError', () => {
        expect(() => decode(1222)).toThrow(`Unable to decode value: "1222". Original error: BUFFER_SHORTAGE`);
      });
    });
  });
});
