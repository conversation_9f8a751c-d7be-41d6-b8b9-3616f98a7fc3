import withMethod from './withMethod';

const statusCode = 405;
const payload = { message: 'Method not allowed' };

describe('withMethod', () => {
  const fn = jest.fn();
  const res = {
    status: jest.fn(),
    json: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('passes', () => {
    const req = {
      method: 'GET',
    };

    withMethod.GET(fn)(req, res);

    expect(res.status).toHaveBeenCalledTimes(0);
    expect(res.json).toHaveBeenCalledTimes(0);
    expect(fn).toHaveBeenCalledWith(req, res);
  });

  it('calls function directly', () => {
    const req = {
      method: 'GET',
    };

    withMethod('POST')(fn)(req, res);

    expect(res.status).toHaveBeenCalledWith(statusCode);
    expect(res.json).toHaveBeenCalledWith(payload);
    expect(fn).toHaveBeenCalledTimes(0);
  });

  it('calls by method', () => {
    const req = {
      method: 'GET',
    };

    withMethod.POST(fn)(req, res);

    expect(res.status).toHaveBeenCalledWith(statusCode);
    expect(res.json).toHaveBeenCalledWith(payload);
    expect(fn).toHaveBeenCalledTimes(0);
  });

  it('calls by not allowed method', () => {
    const req = {
      method: 'GET',
    };

    withMethod('GETTT')(fn)(req, res);

    expect(res.status).toHaveBeenCalledWith(statusCode);
    expect(res.json).toHaveBeenCalledWith(payload);
    expect(fn).toHaveBeenCalledTimes(0);
  });
});
