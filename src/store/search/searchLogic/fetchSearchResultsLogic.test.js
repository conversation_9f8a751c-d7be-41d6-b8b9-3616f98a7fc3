import { createMockStore } from 'redux-logic-test';
import { logic } from 'store/search/searchLogic';
import { searchLocationAvailability } from 'lib/clients/searchLocationAvailability';
import { fetchSearchResults, fetchSearchResultsSuccess, setLoading, fetchSearchResultsFailure } from 'store/search/searchActions';
import { getSearchQuery } from 'store/search/searchSelectors';
import { LIST_SEARCH_LIMIT } from 'config';
import { parseError, captureErrorInSentry } from 'lib/errors';
import { getAccessToken, getFlightBookerToken, getQhUserId } from 'store/user/userSelectors';
import { setPointsLevels } from 'store/pointsConversion/pointsConversionActions';
import { getGeolocation } from 'store/userEnvironment/userEnvironmentSelectors';

jest.mock('store/user/userSelectors');
jest.mock('store/search/searchSelectors');
jest.mock('lib/clients/searchLocationAvailability');
jest.mock('lib/errors/captureErrorInSentry');
jest.mock('store/userEnvironment/userEnvironmentSelectors');
jest.mock('store/pointsConversion/pointsConversionActions');

const initialState = {
  search: {},
  user: {
    authentication: {
      accessToken: 'accessToken123',
    },
    flightBookerToken: 'flightBookerToken123',
    qhUserId: 'qhUserId123',
  },
  userEnvironment: {
    geolocation: null,
  },
};

const tierInstance = {};

const searchQuery = { location: 'Singapore', checkIn: new Date('2018-12-06'), checkOut: new Date('2018-12-08'), minStarRating: 3 };
const searchResponse = {
  meta: {
    totalResults: 1,
    pointsTierInstances: { tierInstance },
  },
  results: [{ property: { name: 'Property Name' } }],
};

const mockGeolocation = {
  city: 'Sydney',
  country: 'Australia',
  region: 'NSW',
};

let store;

describe('fetchSearchResultsLogic', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getSearchQuery.mockReturnValue(searchQuery);
    searchLocationAvailability.mockResolvedValue(searchResponse);
    getGeolocation.mockReturnValue(mockGeolocation);
    getAccessToken.mockReturnValue(initialState.user.authentication.accessToken);
    getFlightBookerToken.mockReturnValue(initialState.user.flightBookerToken);
    getQhUserId.mockReturnValue(initialState.user.qhUserId);
    setPointsLevels.mockReturnValue({ type: 'POINTS_CONVERSION/SET_POINTS_LEVELS', payload: tierInstance });
    store = createMockStore({ initialState, logic });
  });

  describe('when fetching search results', () => {
    it('calls searchLocationAvailability with the correct params, including geolocation from state', async () => {
      store.dispatch(fetchSearchResults());
      await store.whenComplete();
      expect(searchLocationAvailability).toHaveBeenCalledWith(
        expect.objectContaining({
          ...searchQuery,
          limit: LIST_SEARCH_LIMIT,
          accessToken: initialState.user.authentication.accessToken,
          flightBookerToken: initialState.user.flightBookerToken,
          qhUserId: initialState.user.qhUserId,
        }),
      );
    });

    it('dispatches the fetchSearchResultsSuccess action with the geolocation from state', async () => {
      store.dispatch(fetchSearchResults());
      await store.whenComplete();
      expect(store.actions).toEqual(
        expect.arrayContaining([fetchSearchResultsSuccess({ ...searchResponse, geolocation: mockGeolocation })]),
      );
    });

    it('dispatches the fetchSearchResultsSuccess action with null geolocation if it is not in state', async () => {
      getGeolocation.mockReturnValue(null);
      store.dispatch(fetchSearchResults());
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([fetchSearchResultsSuccess({ ...searchResponse, geolocation: null })]));
    });
  });

  describe('with a successful search response', () => {
    it('dispatches the setLoading action', async () => {
      store.dispatch(fetchSearchResults());
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setLoading(true), setLoading(false)]));
    });

    it('sets the tierInstance', async () => {
      store.dispatch(fetchSearchResults());
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setPointsLevels(tierInstance)]));
    });
  });

  describe('with an unsuccessful search response', () => {
    const error = new Error();
    error.response = { status: 500 };

    beforeEach(() => {
      searchLocationAvailability.mockRejectedValue(error);
      store.dispatch(fetchSearchResults());
    });

    it('dispatches the setLoading action', async () => {
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setLoading(true), setLoading(false)]));
    });

    it('dispatches the fetchSearchResultsFailure action', async () => {
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([fetchSearchResultsFailure(parseError(error))]));
    });

    it('does not log the error with sentry', async () => {
      await store.whenComplete();
      expect(captureErrorInSentry).not.toHaveBeenCalled();
    });
  });

  describe('with a general error', () => {
    const error = new Error('Test Error');

    beforeEach(() => {
      getSearchQuery.mockImplementation(() => {
        throw error;
      });
      store.dispatch(fetchSearchResults());
    });

    it('logs the error with sentry', async () => {
      await store.whenComplete();
      expect(captureErrorInSentry).toHaveBeenCalledWith(error);
    });
  });

  describe('with a limit', () => {
    beforeEach(async () => {
      store.dispatch(fetchSearchResults({ limit: 2000 }));
      await store.whenComplete();
    });

    it('passes the custom limit to search', () => {
      expect(searchLocationAvailability).toHaveBeenCalledWith(expect.objectContaining({ limit: 2000 }));
    });
  });

  describe('without a limit', () => {
    beforeEach(async () => {
      store.dispatch(fetchSearchResults());
      await store.whenComplete();
    });

    it('passes the default limit to search', () => {
      expect(searchLocationAvailability).toHaveBeenCalledWith(expect.objectContaining({ limit: LIST_SEARCH_LIMIT }));
    });
  });
});
