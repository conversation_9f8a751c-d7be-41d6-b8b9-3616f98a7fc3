import * as selectors from './userEnvironmentSelectors';

const state = {
  userEnvironment: {
    ipAddress: 'ipAddress',
    deviceFingerprint: 'deviceFingerprint',
    deviceFingerprintError: 'deviceFingerprintError',
    browser: {
      name: 'Chrome',
      type: 'mobile',
    },
    isBot: 'isBot',
    geolocation: {
      city: 'Sydney',
      country: 'Australia',
    },
  },
};

describe('getIpAddress', () => {
  it('returns ipAddress', () => {
    expect(selectors.getIpAddress(state)).toEqual(state.userEnvironment.ipAddress);
  });
});

describe('getDeviceFingerprint', () => {
  it('returns deviceFingerprint', () => {
    expect(selectors.getDeviceFingerprint(state)).toEqual(state.userEnvironment.deviceFingerprint);
  });
});

describe('getDeviceFingerprintError', () => {
  it('returns deviceFingerprintError', () => {
    expect(selectors.getDeviceFingerprintError(state)).toEqual(state.userEnvironment.deviceFingerprintError);
  });
});

describe('getBrowser', () => {
  it('returns browser', () => {
    expect(selectors.getBrowser(state)).toEqual(state.userEnvironment.browser);
  });
});

describe('getIsBot', () => {
  it('returns isBot', () => {
    expect(selectors.getIsBot(state)).toEqual(state.userEnvironment.isBot);
  });
});

describe('getDeviceType', () => {
  it('returns browser type', () => {
    expect(selectors.getDeviceType(state)).toEqual(state.userEnvironment.browser.type);
  });
});

describe('getGeolocation', () => {
  it('returns geolocation data', () => {
    expect(selectors.getGeolocation(state)).toEqual(state.userEnvironment.geolocation);
  });
});
