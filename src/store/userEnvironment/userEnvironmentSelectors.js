import get from 'lodash/get';

export const getIpAddress = (state) => get(state, 'userEnvironment.ipAddress');
export const getDeviceFingerprint = (state) => get(state, 'userEnvironment.deviceFingerprint');
export const getDeviceFingerprintError = (state) => get(state, 'userEnvironment.deviceFingerprintError');
export const getBrowser = (state) => get(state, 'userEnvironment.browser');
export const getIsBot = (state) => get(state, 'userEnvironment.isBot');
export const getDeviceType = (state) => get(state, 'userEnvironment.browser.type');
export const getGeolocation = (state) => get(state, 'userEnvironment.geolocation');
