import checkGeolocationClient from 'lib/clients/checkGeolocation';
import { getGeolocation } from 'store/userEnvironment/userEnvironmentSelectors';
import { setGeolocation, fetchGeolocation } from 'store/userEnvironment/userEnvironmentActions';
import { fetchGeolocationLogic } from './fetchGeolocationLogic';

jest.mock('lib/clients/checkGeolocation', () => jest.fn());
jest.mock('store/userEnvironment/userEnvironmentSelectors', () => ({
  getGeolocation: jest.fn(),
}));
jest.mock('store/userEnvironment/userEnvironmentActions', () => ({
  setGeolocation: jest.fn(() => ({ type: 'userEnvironment/setGeolocation' })),
  fetchGeolocation: 'userEnvironment/fetchGeolocation',
}));

describe('fetchGeolocationLogic', () => {
  let dispatch;
  let getState;
  let done;

  beforeEach(() => {
    dispatch = jest.fn();
    getState = jest.fn();
    done = jest.fn();
    jest.clearAllMocks();
  });

  it('should not fetch geolocation if it already exists in the state', async () => {
    const mockGeolocation = { country: 'AU', region: 'NSW' };
    getGeolocation.mockReturnValue(mockGeolocation);
    getState.mockReturnValue({});

    await fetchGeolocationLogic.process({ getState, action: { type: fetchGeolocation } }, dispatch, done);

    expect(getGeolocation).toHaveBeenCalledWith({});
    expect(checkGeolocationClient).not.toHaveBeenCalled();
    expect(dispatch).not.toHaveBeenCalled();
    expect(done).toHaveBeenCalledTimes(1);
  });

  it('should fetch and set geolocation if it does not exist in the state', async () => {
    const mockGeolocation = { country: 'US', region: 'CA' };
    getGeolocation.mockReturnValue(null);
    checkGeolocationClient.mockResolvedValue(mockGeolocation);
    getState.mockReturnValue({});

    await fetchGeolocationLogic.process({ getState, action: { type: fetchGeolocation } }, dispatch, done);

    expect(getGeolocation).toHaveBeenCalledWith({});
    expect(checkGeolocationClient).toHaveBeenCalledTimes(1);
    expect(dispatch).toHaveBeenCalledWith(setGeolocation(mockGeolocation));
    expect(done).toHaveBeenCalledTimes(1);
  });
});
