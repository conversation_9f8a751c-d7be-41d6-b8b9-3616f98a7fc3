import { createAsyncLogic } from 'lib/logic';
import checkGeolocationClient from 'lib/clients/checkGeolocation';
import { getGeolocation } from 'store/userEnvironment/userEnvironmentSelectors';
import { setGeolocation, fetchGeolocation } from 'store/userEnvironment/userEnvironmentActions';

export const fetchGeolocationLogic = createAsyncLogic({
  type: fetchGeolocation,
  async process({ getState }, dispatch) {
    const state = getState();
    const existingGeolocation = getGeolocation(state);

    if (!existingGeolocation) {
      const geolocation = await checkGeolocationClient();
      dispatch(setGeolocation(geolocation));
    }
  },
});
