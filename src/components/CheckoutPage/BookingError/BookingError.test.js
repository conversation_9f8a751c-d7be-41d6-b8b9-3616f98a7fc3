import React from 'react';
import BookingError from './BookingError';
import { mountUtils } from 'test-utils';

const decorators = { theme: true };
const render = (props) => mountUtils(<BookingError {...props} />, { decorators });

const scrollIntoViewMock = jest.fn();
window.HTMLElement.prototype.scrollIntoView = scrollIntoViewMock;

const customError = [{ code: 'payment_failed_credit_card_expired' }];
const agentResolvableError = [{ code: 'payment_failed', agentResolvable: true }];
const nonAgentResolvableError = [{ code: 'payment_failed', agentResolvable: false }];
const type = 'credit card';

beforeEach(() => {
  jest.clearAllMocks();
});

describe('with no errors', () => {
  it('does not render anything', () => {
    const { wrapper } = render({ errors: [], type });
    expect(wrapper).toBeEmptyRender();
  });
});

describe('with the default error definition', () => {
  it('renders the icon', () => {
    const { findByTestId } = render({ errors: nonAgentResolvableError, type });

    expect(findByTestId('error-icon')).toExist();
  });

  it('renders the heading', () => {
    const { findByTestId } = render({ errors: nonAgentResolvableError, type });

    expect(findByTestId('error-heading')).toExist();
  });

  it('renders the description', () => {
    const { findByTestId } = render({ errors: nonAgentResolvableError, type });

    expect(findByTestId('error-icon')).toExist();
  });

  it('renders a custom description based on the error type', () => {
    const { findByTestId } = render({ errors: nonAgentResolvableError, type: 'voucher' });

    expect(findByTestId('error-description').text()).toMatch(/Please check your voucher details/);
  });
});

describe('with a customised error definition', () => {
  it('renders a custom heading', () => {
    const { findByTestId } = render({ errors: customError, type });

    expect(findByTestId('error-heading').text()).toEqual('Your credit card has expired');
  });

  it('renders a custom description', () => {
    const { findByTestId } = render({ errors: customError, type });

    expect(findByTestId('error-description').text()).toEqual('Please check your card details or contact your financial institution.');
  });
});

describe('when the error is agent_resolvable', () => {
  it('renders the contact us link', () => {
    const { findByTestId } = render({ errors: agentResolvableError, type });

    expect(findByTestId('contact-link')).toExist();
  });

  describe('when credit card errors occur', () => {
    it('scrolls the view to the error message', () => {
      render({ errors: agentResolvableError, type });

      expect(scrollIntoViewMock).toHaveBeenCalled();
    });
  });

  describe('when points pay errors occur', () => {
    it('scrollIntoView is NOT called', () => {
      const type = 'Qantas Points';
      render({ errors: agentResolvableError, type });

      expect(scrollIntoViewMock).not.toHaveBeenCalled();
    });
  });
});
