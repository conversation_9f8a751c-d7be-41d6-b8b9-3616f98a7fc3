import isValidVoucher from './isValidVoucher';

describe('isValidVoucher', () => {
  test('returns true if voucher code is valid', () => {
    expect(isValidVoucher('7G6VXEP9R3YEPV2Y')).toBe(true);
    expect(isValidVoucher('63781602100000039')).toBe(true);
  });

  test('returns false if voucher code is invalid', () => {
    expect(isValidVoucher('12R35HQ')).toBe(false);
    expect(isValidVoucher('12R3768493512R35HQFqNq2P5HQ')).toBe(false);
    expect(isValidVoucher('874026117895037726519487720')).toBe(false);
    expect(isValidVoucher('12345')).toBe(false);
    expect(isValidVoucher('abc')).toBe(false);
    expect(isValidVoucher('456764567756476%&#')).toBe(false);
  });
});
