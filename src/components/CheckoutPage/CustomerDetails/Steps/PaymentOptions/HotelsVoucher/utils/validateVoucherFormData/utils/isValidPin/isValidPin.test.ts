import isValidPin from './isValidPin';

describe('isValidPin', () => {
  test('returns true if the pin is valid', () => {
    expect(isValidPin('0000')).toBe(true);
    expect(isValidPin('1234')).toBe(true);
  });

  test('returns false if the pin is invalid', () => {
    expect(isValidPin('123')).toBe(false);
    expect(isValidPin('12345')).toBe(false);
    expect(isValidPin('abc')).toBe(false);
    expect(isValidPin('%&#')).toBe(false);
  });
});
