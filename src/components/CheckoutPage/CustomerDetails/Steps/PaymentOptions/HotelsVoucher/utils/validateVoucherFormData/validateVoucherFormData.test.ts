import validateVoucherFormData from './validateVoucherFormData';

describe('validateVoucherFormData', () => {
  test('returns correct error message if voucher code is empty', () => {
    const result = validateVoucherFormData({
      isPinRequired: false,
      voucherCode: '',
      pin: '',
    });

    expect(result).toEqual({
      voucherCode: 'Voucher code is required',
      pin: '',
    });
  });

  test('returns correct error message if voucher code is incorrect', () => {
    const result = validateVoucherFormData({
      isPinRequired: false,
      voucherCode: '123/SSR',
      pin: '',
    });

    expect(result).toEqual({
      voucherCode: 'Voucher code is incorrect',
      pin: '',
    });
  });

  test('returns correct error message if pin is empty', () => {
    const result = validateVoucherFormData({
      isPinRequired: true,
      voucherCode: '7G6VXEP9R3YEPV2Y',
      pin: '',
    });

    expect(result).toEqual({
      voucherCode: '',
      pin: 'PIN is required',
    });
  });

  test('returns correct error message if pin is incorrect', () => {
    const result = validateVoucherFormData({
      isPinRequired: true,
      voucherCode: '7G6VXEP9R3YEPV2Y',
      pin: '123/SSR',
    });

    expect(result).toEqual({
      voucherCode: '',
      pin: 'PIN is incorrect',
    });
  });

  test('returns null if voucher code and pin are correct when pin is required', () => {
    const result = validateVoucherFormData({
      isPinRequired: true,
      voucherCode: 'GCEX6JJPXMEAWDHX',
      pin: '1234',
    });

    expect(result).toBeNull();
  });

  test('returns null if voucher code and pin are correct when pin is not required', () => {
    const result = validateVoucherFormData({
      isPinRequired: false,
      voucherCode: '7G6VXEP9R3YEPV2Y',
      pin: '',
    });

    expect(result).toBeNull();
  });
});
