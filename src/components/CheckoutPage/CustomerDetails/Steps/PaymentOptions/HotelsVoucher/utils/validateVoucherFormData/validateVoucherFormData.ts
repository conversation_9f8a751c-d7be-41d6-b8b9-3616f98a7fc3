import isValidPin from './utils/isValidPin';
import isValidVoucher from './utils/isValidVoucher';

type VoucherFormData = {
  isPinRequired: boolean;
  voucherCode: string;
  pin: string;
};

const validateVoucherFormData = ({ isPinRequired, voucherCode, pin }: VoucherFormData) => {
  const errorMessages = {
    voucherCode: '',
    pin: '',
  };

  if (!voucherCode) {
    errorMessages['voucherCode'] = 'Voucher code is required';
  } else {
    if (!isValidVoucher(voucherCode)) {
      errorMessages['voucherCode'] = 'Voucher code is incorrect';
    }
  }
  if (isPinRequired) {
    if (!pin) {
      errorMessages['pin'] = 'PIN is required';
    } else {
      if (!isValidPin(pin)) {
        errorMessages['pin'] = 'PIN is incorrect';
      }
    }
  }

  return errorMessages['voucherCode'] || errorMessages['pin'] ? errorMessages : null;
};

export default validateVoucherFormData;
