import React from 'react';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { act } from 'react-dom/test-utils';
import { mountUtils } from 'test-utils';
import checkVoucherBalance from 'lib/clients/checkVoucherBalance';
import checkIsVoucherPinRequired from 'lib/clients/checkIsVoucherPinRequired';
import { getTotalPayableAtBooking, getPropertyId } from 'store/quote/quoteSelectors';
import { getVoucherAmount } from 'store/checkout/checkoutSelectors';
import { updatePayments } from 'store/checkout/checkoutActions';
import { getVoucherErrors } from 'store/booking/bookingSelectors';
import HotelsVoucher, { GET_VOUCHER_BALANCE_FAILED_ERROR } from './HotelsVoucher';
import { useDataLayer } from 'hooks/useDataLayer';
import checkVoucher from 'lib/clients/checkVoucher';
import { useViiVoucher } from './hooks/useViiVoucher';
import { useViiVoucherMaintenance } from './hooks/useViiVoucherMaintenance';

jest.mock('store/quote/quoteSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('lib/clients/checkVoucherBalance');
jest.mock('lib/clients/checkIsVoucherPinRequired');
jest.mock('store/booking/bookingSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('lib/clients/checkVoucher');
jest.mock('./hooks/useViiVoucher');
jest.mock('./hooks/useViiVoucherMaintenance');

mountUtils.mockComponent('FieldError');
mountUtils.mockComponent('VoucherTerms');
mountUtils.mockComponent('BookingError');

const render = () => mountUtils(<HotelsVoucher />, { decorators: { store: true } });

jest.useFakeTimers();

let totalPayableAtBooking = new Decimal(100);
const voucherAmount = new Decimal(0);
const voucherType = 'credit_voucher';
const voucherCode = '103260000001062998';
const pin = '1563';
const voucherErrors = [];
const emitInteractionEvent = jest.fn();
const propertyId = 101;

const simulateVoucherCodeChange = (findByTestId, find, voucherCode) => {
  findByTestId('add-voucher-button').simulate('click');
  find('Input[name="voucherCode"]').simulate('change', { target: { value: voucherCode } });
  jest.advanceTimersByTime(300);
};

beforeEach(() => {
  getTotalPayableAtBooking.mockReturnValue(totalPayableAtBooking);
  getPropertyId.mockReturnValue(propertyId);
  getVoucherAmount.mockReturnValue(voucherAmount);
  getVoucherErrors.mockReturnValue(voucherErrors);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useViiVoucher.mockReturnValue({
    isReady: true,
    isViiVoucher: true,
  });
  useViiVoucherMaintenance.mockReturnValue({
    isReady: true,
    isViiVoucherMaintenance: false,
  });
});

afterEach(() => {
  jest.resetAllMocks();
});

it('renders the ADD VOUCHER button', () => {
  const { findByTestId } = render();
  expect(findByTestId('add-voucher-button')).toHaveText('ADD');
});

it('disables the ADD VOUCHER button when voucher maintenance feature flag is on', () => {
  useViiVoucherMaintenance.mockReturnValue({
    isReady: true,
    isViiVoucherMaintenance: true,
  });
  const { findByTestId } = render();

  expect(findByTestId('add-voucher-button')).toHaveProp({ disabled: true });
});

describe('with an initial voucher amount greater than zero', () => {
  let voucherAmount = new Decimal(100);

  beforeEach(() => {
    getVoucherAmount.mockReturnValue(voucherAmount);
  });

  it('renders the voucher amount', () => {
    const { find } = render();
    expect(find('Currency')).toHaveProp({ amount: voucherAmount, currency: 'AUD' });
  });
});

describe('with a voucher error caused by booking submission', () => {
  const voucherErrors = [{ code: 'payment_failed_voucher', message: 'message' }];

  beforeEach(() => {
    getVoucherErrors.mockReturnValue(voucherErrors);
  });

  it('renders the BookingError with voucher errors', () => {
    const { find } = render();
    expect(find('BookingError')).toHaveProp({
      errors: voucherErrors,
      type: 'voucher',
    });
  });

  it('clears the voucher code and amount', () => {
    const { decorators } = render();
    expect(decorators.store.dispatch).toHaveBeenCalledWith(
      updatePayments({ voucher: { type: null, code: null, amount: new Decimal(0), pin: null } }),
    );
  });
});

describe('adding a voucher', () => {
  let voucherAmount = new Decimal(100);

  it('renders the voucher terms link', async () => {
    const { find, findByTestId } = render();

    findByTestId('add-voucher-button').simulate('click');
    expect(find('VoucherTerms')).toExist();
  });

  describe('when vii voucher feature flag is off', () => {
    beforeEach(() => {
      useViiVoucher.mockReturnValue({
        isReady: true,
        isViiVoucher: false,
      });
    });

    it('renders the apply button as disabled', () => {
      const { findByTestId } = render();

      findByTestId('add-voucher-button').simulate('click');
      expect(findByTestId('apply-voucher-button')).toHaveProp({ disabled: true });
    });

    it('enables the apply button after entering a voucher code', async () => {
      const { findByTestId, find, wrapper } = render();

      await act(async () => {
        await wrapper.update();
        simulateVoucherCodeChange(findByTestId, find, voucherCode);
      });

      await wrapper.update();

      expect(findByTestId('apply-voucher-button')).toHaveProp({ disabled: false });
    });

    it('does not check if voucher pin is required', async () => {
      const { wrapper, findByTestId, find } = render();

      await act(async () => {
        await wrapper.update();
        simulateVoucherCodeChange(findByTestId, find, voucherCode);
      });

      await wrapper.update();

      expect(checkIsVoucherPinRequired).not.toHaveBeenCalled();
    });

    it('checks the voucher code when clicking apply', async () => {
      checkVoucher.mockReturnValue(Promise.resolve({ amount: voucherAmount }));

      const { findByTestId, find, wrapper } = render();

      await act(async () => {
        await wrapper.update();
        simulateVoucherCodeChange(findByTestId, find, voucherCode);
      });

      findByTestId('apply-voucher-button').simulate('click');

      await act(async () => {
        await flushPromises();
      });

      expect(checkVoucher).toHaveBeenCalledWith({ voucherCode, totalPayableAtBooking, propertyId });
    });

    it('updates form data with returned voucher amount and code', async () => {
      checkVoucher.mockReturnValue(Promise.resolve({ amount: voucherAmount }));

      const { findByTestId, find, wrapper, decorators } = render();

      await act(async () => {
        await wrapper.update();
        simulateVoucherCodeChange(findByTestId, find, voucherCode);
      });

      findByTestId('apply-voucher-button').simulate('click');

      await act(async () => {
        await flushPromises();
      });

      expect(decorators.store.dispatch).toHaveBeenCalledWith(updatePayments({ voucher: { amount: voucherAmount, code: voucherCode } }));
    });
  });

  it('checks if voucher pin is required after entering more than 4 characters', async () => {
    const { wrapper, findByTestId, find } = render();

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
      await wrapper.update();
      expect(findByTestId('loading-indicator')).toExist();
    });

    expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode });
  });

  it('enables the apply button after entering a voucher code if voucher pin check is finished and pin is not required', async () => {
    const { findByTestId, find, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: false,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    await act(async () => {
      await flushPromises();
    });

    wrapper.update();

    expect(findByTestId('apply-voucher-button')).toHaveProp({ disabled: false });
  });

  it('enables the apply button after entering a voucher code and pin if voucher pin check is finished and pin is required', async () => {
    const { findByTestId, find, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    await act(async () => {
      await flushPromises();
    });

    wrapper.update();

    find('#voucherPin')
      .at(0)
      .simulate('change', { target: { value: pin } });

    expect(findByTestId('apply-voucher-button')).toHaveProp({ disabled: false });
  });

  it('displays voucher pin check error', async () => {
    const errorMessage = 'errorMessage';
    const { wrapper, findByTestId, find } = render();

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    checkIsVoucherPinRequired.mockRejectedValue(
      new Error({
        response: {
          data: {
            errorMessage,
          },
        },
      }),
    );

    expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode });

    await wrapper.update();

    find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: { message: errorMessage } }));
  });

  it("displays default voucher pin check error if the response doesn't include any error messages", async () => {
    const { wrapper, findByTestId, find } = render();

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    checkIsVoucherPinRequired.mockRejectedValue(new Error("Can't use this voucher"));

    expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode });

    await wrapper.update();

    find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: { message: GET_VOUCHER_BALANCE_FAILED_ERROR } }));
  });

  it('calls voucher pin check again after voucher code change if there is an error', async () => {
    const { wrapper, findByTestId, find } = render();

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    checkIsVoucherPinRequired.mockRejectedValue(new Error("Can't use this voucher"));

    expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode });

    await wrapper.update();

    find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: { message: GET_VOUCHER_BALANCE_FAILED_ERROR } }));

    const newVoucherCode = 'new voucher code';

    await act(async () => {
      find('Input[name="voucherCode"]').simulate('change', { target: { value: newVoucherCode } });
      await wrapper.update();
      jest.advanceTimersByTime(300);
      expect(checkIsVoucherPinRequired).toHaveBeenCalledWith({ voucherCode: newVoucherCode });
    });
  });

  it('displays pin input if voucher pin is required', async () => {
    const { wrapper, findByTestId, find } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    await wrapper.update();

    expect(find('#voucherPin')).toExist();
  });

  it('checks the voucher code when clicking apply', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount }));
    const { findByTestId, find, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: false,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    await act(async () => {
      await flushPromises();
    });

    findByTestId('apply-voucher-button').simulate('click');

    await act(async () => {
      await flushPromises();
    });

    expect(checkVoucherBalance).toHaveBeenCalledWith({ voucherCode, pin: '' });
  });

  it('validates form, displays errors and does not check voucher balance if voucher code is not provided', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    const { findByTestId, find, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, '');
    });

    await act(async () => {
      await flushPromises();
    });

    await wrapper.update();

    findByTestId('apply-voucher-button').simulate('click');

    await act(async () => {
      await flushPromises();
    });

    expect(checkVoucherBalance).not.toHaveBeenCalled();
    find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: { message: 'Voucher code is required' } }));
  });

  it('validates form, displays errors and does not check voucher balance if pin is not provided', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    const { findByTestId, find, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, '103260000001063219');
    });

    await act(async () => {
      await flushPromises();
    });

    await wrapper.update();

    findByTestId('apply-voucher-button').simulate('click');

    await act(async () => {
      await flushPromises();
    });

    expect(checkVoucherBalance).not.toHaveBeenCalled();
    find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: { message: 'PIN is required' } }));
  });

  it('validates form, displays errors and does not check voucher balance if voucher code or pin are incorrect', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    const { findByTestId, find, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, 'ABC1235456444556454Q#');
    });

    await act(async () => {
      await flushPromises();
    });

    await wrapper.update();

    find('#voucherPin')
      .at(0)
      .simulate('change', { target: { value: 'A99' } });

    findByTestId('apply-voucher-button').simulate('click');

    await act(async () => {
      await flushPromises();
    });

    expect(checkVoucherBalance).not.toHaveBeenCalled();
    find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: { message: 'Voucher code is incorrect' } }));
    find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: { message: 'PIN is incorrect' } }));
  });

  it('checks the voucher code amount with voucher code and pin when clicking apply', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    const { findByTestId, find, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    await act(async () => {
      await flushPromises();
    });

    await wrapper.update();

    find('#voucherPin')
      .at(0)
      .simulate('change', { target: { value: pin } });

    findByTestId('apply-voucher-button').simulate('click');

    await act(async () => {
      await flushPromises();
    });

    expect(checkVoucherBalance).toHaveBeenCalledWith({ voucherCode, pin });
  });

  it('dispatches an event to the data layer', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount }));
    const { findByTestId, find, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: false,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    await act(async () => {
      await flushPromises();
    });

    findByTestId('apply-voucher-button').simulate('click');

    await act(async () => {
      await flushPromises();
    });

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Voucher Link',
      value: 'Voucher Succeeded',
    });
  });

  it('updates form data with returned voucher amount and code', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    const { findByTestId, find, decorators, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: false,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    await act(async () => {
      await flushPromises();
    });

    findByTestId('apply-voucher-button').simulate('click');

    await act(async () => {
      await flushPromises();
    });

    expect(decorators.store.dispatch).toHaveBeenCalledWith(
      updatePayments({ voucher: { type: voucherType, code: voucherCode, amount: voucherAmount } }),
    );
  });

  it('updates form data with returned voucher amount, code and pin', async () => {
    checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
    const { findByTestId, find, decorators, wrapper } = render();

    checkIsVoucherPinRequired.mockReturnValue({
      pinRequired: true,
    });

    await act(async () => {
      await wrapper.update();
      simulateVoucherCodeChange(findByTestId, find, voucherCode);
    });

    await act(async () => {
      await flushPromises();
    });

    await wrapper.update();

    find('#voucherPin')
      .at(0)
      .simulate('change', { target: { value: pin } });

    findByTestId('apply-voucher-button').simulate('click');

    await act(async () => {
      await flushPromises();
    });

    expect(decorators.store.dispatch).toHaveBeenCalledWith(
      updatePayments({ voucher: { type: voucherType, code: voucherCode, amount: voucherAmount, pin } }),
    );
  });

  describe('with a voucher amount that is more than the booking value', () => {
    it('allocates the voucher amount equal to the booking total', async () => {
      totalPayableAtBooking = voucherAmount.minus(10);
      getTotalPayableAtBooking.mockReturnValue(totalPayableAtBooking);
      checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
      const { findByTestId, find, decorators, wrapper } = render();

      checkIsVoucherPinRequired.mockReturnValue({
        pinRequired: false,
      });

      await act(async () => {
        await wrapper.update();
        simulateVoucherCodeChange(findByTestId, find, voucherCode);
      });

      await act(async () => {
        await flushPromises();
      });

      findByTestId('apply-voucher-button').simulate('click');

      await act(async () => {
        await flushPromises();
      });

      expect(decorators.store.dispatch).toHaveBeenCalledWith(
        updatePayments({ voucher: { type: voucherType, code: voucherCode, amount: totalPayableAtBooking } }),
      );
    });
  });

  describe('cancelling an edit', () => {
    it('hides the form', async () => {
      const { findByTestId, find } = render();

      findByTestId('add-voucher-button').simulate('click');
      expect(find('Input[name="voucherCode"]')).toExist();
      findByTestId('cancel-edit-voucher-button').simulate('click');
      expect(find('Input[name="voucherCode"]')).not.toExist();
    });
  });

  describe('removing the voucher', () => {
    it('displays the ADD button', async () => {
      checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount }));
      const { findByTestId, find, wrapper } = render();

      checkIsVoucherPinRequired.mockReturnValue({
        pinRequired: false,
      });

      await act(async () => {
        await wrapper.update();
        simulateVoucherCodeChange(findByTestId, find, voucherCode);
      });

      await act(async () => {
        await flushPromises();
      });

      findByTestId('apply-voucher-button').simulate('click');

      await act(async () => {
        await flushPromises();
      });
      await wrapper.update();

      findByTestId('remove-voucher-button').simulate('click');
      expect(findByTestId('add-voucher-button')).toHaveText('ADD');
      find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: null }));
    });

    it('clears the voucher formData', async () => {
      checkVoucherBalance.mockReturnValue(Promise.resolve({ availableBalance: voucherAmount, voucherPaymentType: voucherType }));
      const { findByTestId, find, wrapper, decorators } = render();

      checkIsVoucherPinRequired.mockReturnValue({
        pinRequired: false,
      });

      await act(async () => {
        await wrapper.update();
        simulateVoucherCodeChange(findByTestId, find, voucherCode);
      });

      await act(async () => {
        await flushPromises();
      });

      findByTestId('apply-voucher-button').simulate('click');

      await act(async () => {
        await flushPromises();
      });

      await wrapper.update();

      findByTestId('remove-voucher-button').simulate('click');
      expect(decorators.store.dispatch).toHaveBeenCalledWith(
        updatePayments({ voucher: { type: null, code: null, amount: new Decimal(0), pin: null } }),
      );
    });
  });

  describe('with an invalid voucher code', () => {
    let find;
    let findByTestId;
    let wrapper;

    const fillInvalidVoucherAndApply = async () => {
      checkVoucherBalance.mockRejectedValue({ response: { data: { errorMessage: 'Invalid voucher code' } } });
      ({ findByTestId, find, wrapper } = render());

      checkIsVoucherPinRequired.mockReturnValue({
        pinRequired: false,
      });

      await act(async () => {
        await wrapper.update();
        simulateVoucherCodeChange(findByTestId, find, voucherCode);
      });

      await act(async () => {
        await flushPromises();
      });

      findByTestId('apply-voucher-button').simulate('click');

      await act(async () => {
        await flushPromises();
      });
      wrapper.update();
    };

    it("displays a default error if the response doesn't include any error messages", async () => {
      await fillInvalidVoucherAndApply();
      find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: { message: 'Invalid voucher code' } }));
    });

    it('displays the response error message if the response includes one', async () => {
      const error = new Error();
      error.response = {
        data: {
          error: "Can't use this voucher",
        },
      };

      await fillInvalidVoucherAndApply(error);
      find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: { message: error.response.data.error } }));
    });

    it('dispatches an event to the data layer', async () => {
      await fillInvalidVoucherAndApply();

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Voucher Link',
        value: 'Voucher Invalid',
      });
    });

    it('clears the error when changing the voucher code', async () => {
      await fillInvalidVoucherAndApply();

      find('Input[name="voucherCode"]').simulate('change', { target: { value: 'new voucher code' } });
      find('FieldError').forEach((fieldError) => expect(fieldError).toHaveProp({ error: null }));
    });
  });
});
