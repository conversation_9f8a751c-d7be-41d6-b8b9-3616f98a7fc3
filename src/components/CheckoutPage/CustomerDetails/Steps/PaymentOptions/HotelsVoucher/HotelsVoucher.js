import React, { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import get from 'lodash/get';
import { useSelector, useDispatch } from 'react-redux';
import { Box, Flex, Button, Input, Text, Wrapper, NakedButton, Icon, LoadingIndicator } from '@qga/roo-ui/components';
// eslint-disable-next-line import/no-named-as-default
import Decimal from 'decimal.js';
import { getVoucherErrors } from 'store/booking/bookingSelectors';
import { getTotalPayableAtBooking, getPropertyId } from 'store/quote/quoteSelectors';
import { getVoucherAmount } from 'store/checkout/checkoutSelectors';
import { updatePayments } from 'store/checkout/checkoutActions';
import Currency from 'components/Currency';
import { FieldLabel, FieldError } from '@qga/components';
import VoucherTerms from './VoucherTerms';
import BookingError from 'components/CheckoutPage/BookingError';
import ErrorAlert from 'components/CheckoutPage/ErrorAlert';
import { PaymentMethodSummaryButton } from 'components/CheckoutPage/CustomerDetails/Steps/PaymentOptions/primitives';
import { useDataLayer } from 'hooks/useDataLayer';
import { HOTELS_BRAND_NAME } from 'config';
import checkIsVoucherPinRequired from 'lib/clients/checkIsVoucherPinRequired';
import debounce from 'lodash/debounce';
import checkVoucherBalance from 'lib/clients/checkVoucherBalance';
import checkVoucher from 'lib/clients/checkVoucher';
import { useViiVoucher } from './hooks/useViiVoucher';
import { useViiVoucherMaintenance } from './hooks/useViiVoucherMaintenance';
import validateVoucherFormData from './utils/validateVoucherFormData';

export const GET_VOUCHER_BALANCE_FAILED_ERROR = 'The voucher details cannot be verified at this time. Please try again later.';

const STATES = {
  INITIAL: 'initial',
  EDITING: 'editing',
  ALLOCATED: 'allocated',
};

const HotelsVoucher = React.memo(({ ...rest }) => {
  const dispatch = useDispatch();
  const voucherAmount = useSelector(getVoucherAmount);
  const totalPayableAtBooking = useSelector(getTotalPayableAtBooking);
  const propertyId = useSelector(getPropertyId);
  const [state, setState] = useState(voucherAmount.greaterThan(0) ? STATES.ALLOCATED : STATES.INITIAL);
  const voucherErrors = useSelector(getVoucherErrors);
  const { emitInteractionEvent } = useDataLayer();
  const [voucherCode, setVoucherCode] = useState('');
  const [pin, setPin] = useState('');
  const [error, setError] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isPinRequired, setIsPinRequired] = useState(false);

  // TODOS: Remove both feature flags when voucher integration is released
  const { isViiVoucher, isReady } = useViiVoucher();
  const { isViiVoucherMaintenance } = useViiVoucherMaintenance();

  const addVoucher = useCallback(() => {
    setState(STATES.EDITING);
    emitInteractionEvent({ type: 'Voucher Link', value: 'Add Button Selected' });
  }, [emitInteractionEvent]);

  const applyVoucher = useCallback(async () => {
    setError({});

    if (isViiVoucher) {
      const errorMessages = validateVoucherFormData({
        voucherCode,
        pin,
        isPinRequired,
      });

      if (errorMessages) {
        setError(errorMessages);
        return;
      }
    }

    try {
      let amountToAllocate;
      let voucherType;

      if (isViiVoucher) {
        const { availableBalance, voucherPaymentType } = await checkVoucherBalance({ voucherCode, pin });
        amountToAllocate = Decimal.min(availableBalance, totalPayableAtBooking);
        voucherType = voucherPaymentType;
      } else {
        const { amount } = await checkVoucher({ voucherCode, totalPayableAtBooking, propertyId });
        amountToAllocate = Decimal.min(amount, totalPayableAtBooking);
      }
      dispatch(
        updatePayments({
          voucher: {
            code: voucherCode,
            amount: amountToAllocate,
            ...(isViiVoucher && voucherType && { type: voucherType }),
            ...(isViiVoucher && pin && { pin }),
          },
        }),
      );
      setState(STATES.ALLOCATED);
      emitInteractionEvent({ type: 'Voucher Link', value: 'Voucher Succeeded' });
    } catch (e) {
      const errorMessage = isViiVoucher
        ? get(e, 'response.data.errorMessage') || GET_VOUCHER_BALANCE_FAILED_ERROR
        : get(e, 'response.data.error') || 'Invalid voucher code';

      setError({ validation: errorMessage });
      emitInteractionEvent({ type: 'Voucher Link', value: 'Voucher Invalid' });
    }
  }, [dispatch, emitInteractionEvent, propertyId, totalPayableAtBooking, isViiVoucher, voucherCode, pin, isPinRequired]);

  const removeVoucher = useCallback(() => {
    dispatch(updatePayments({ voucher: { type: null, code: null, amount: new Decimal(0), pin: null } }));
    reset();
  }, [dispatch]);

  const reset = () => {
    setVoucherCode('');
    setPin('');
    setIsPinRequired(false);
    setState(STATES.INITIAL);
    setError({});
  };

  useEffect(() => {
    if (voucherErrors.length) {
      setState(STATES.EDITING);
      dispatch(updatePayments({ voucher: { type: null, code: null, amount: new Decimal(0), pin: null } }));
    }
  }, [dispatch, voucherErrors.length]);

  const showVoucherAmount = state === STATES.ALLOCATED && voucherAmount && !voucherAmount.isZero();

  const onVoucherCodeChange = debounce(async (e) => {
    const newVoucherCode = e.target.value;
    setError({});
    setPin('');

    if (!isViiVoucher) {
      setVoucherCode(newVoucherCode);
      return;
    }

    const shouldCheckIsVoucherPinRequired = error || (newVoucherCode.length > 4 && newVoucherCode.slice(0, 5) !== voucherCode?.slice(0, 5));

    setVoucherCode(newVoucherCode);

    if (newVoucherCode.length < 5) {
      setIsPinRequired(false);
      return;
    }

    if (shouldCheckIsVoucherPinRequired) {
      setIsLoading(true);
      try {
        const { pinRequired } = await checkIsVoucherPinRequired({ voucherCode: newVoucherCode });
        setIsPinRequired(pinRequired);
      } catch (error) {
        const errorMessage = get(error, 'response.data.errorMessage', GET_VOUCHER_BALANCE_FAILED_ERROR);
        setError({ validation: errorMessage });
      } finally {
        setIsLoading(false);
      }
    }
  }, 300);

  const onPinChange = (e) => {
    const newPin = e.target.value;
    setError({});
    setPin(newPin);
  };

  if (!isReady) {
    return null;
  }

  return (
    <Wrapper {...rest}>
      <Flex
        flexDirection="column"
        padding={4}
        borderColor="greys.alto"
        border={1}
        borderBottom={state === STATES.EDITING ? 0 : 1}
        borderRadius={state === STATES.EDITING ? 'exageratedRoundTopOnly' : 'exagerated'}
      >
        <Flex justifyContent="space-between" width="100%" alignItems="center">
          <Flex justifyContent="space-between" flexDirection={['column', 'row']} alignItems={['flex-start', 'center']} width="100%">
            <Text fontWeight="bold" fontSize={['sm', 'base']}>
              {HOTELS_BRAND_NAME} voucher
            </Text>
            <Box mr={[0, 4]}>
              {showVoucherAmount && <Currency amount={voucherAmount} currency="AUD" fontSize="sm" data-testid="voucher-amount" />}
            </Box>
          </Flex>
          {state === STATES.INITIAL && (
            <PaymentMethodSummaryButton
              onClick={addVoucher}
              variant="primary"
              data-testid="add-voucher-button"
              disabled={isViiVoucherMaintenance}
            >
              ADD
            </PaymentMethodSummaryButton>
          )}
          {state === STATES.EDITING && (
            <NakedButton onClick={reset} data-testid="cancel-edit-voucher-button">
              <Icon name="close" color="greys.steel" />
            </NakedButton>
          )}
          {state === STATES.ALLOCATED && (
            <PaymentMethodSummaryButton onClick={removeVoucher} data-testid="remove-voucher-button">
              REMOVE
            </PaymentMethodSummaryButton>
          )}
        </Flex>
        {isViiVoucherMaintenance && (
          <Box mt={4}>
            <ErrorAlert
              heading="Voucher system maintenance"
              description="We are currently performing maintenance to improve our voucher system. Thanks for your patience!"
            />
          </Box>
        )}
      </Flex>
      {state === STATES.EDITING && (
        <Box padding={4} borderColor="greys.alto" border={1} borderRadius="exageratedRoundBottomOnly">
          <BookingError errors={voucherErrors} type="voucher" />
          <Flex flexDirection="column">
            <Flex flexDirection={['column', 'row']}>
              <Box width={['100%', null, '50%']} mr={[0, 4]}>
                <FieldLabel htmlFor="voucherCode">Credit note or voucher code</FieldLabel>
                <Box>
                  <Box position="relative">
                    <Input
                      id="voucherCode"
                      name="voucherCode"
                      inputMode="text"
                      pattern="[A-Za-z0-9]*"
                      autocomplete="off"
                      initialValue={voucherCode}
                      onChange={onVoucherCodeChange}
                      error={!!error?.voucherCode}
                    />
                    {isLoading && (
                      <LoadingIndicator
                        data-testid="loading-indicator"
                        size={8}
                        position="absolute"
                        right="10px"
                        top="50%"
                        transform="translateY(-50%)"
                      />
                    )}
                  </Box>
                  <FieldError error={error?.voucherCode && { message: error.voucherCode }} mb={3} />
                </Box>
              </Box>
              {isPinRequired && (
                <Box width={['100%', null, '20%']} mr={[0, 8]}>
                  <FieldLabel htmlFor="voucherPin">Pin</FieldLabel>
                  <Input
                    id="voucherPin"
                    name={`voucherPin${uuidv4()}`}
                    type="password"
                    autoComplete="new-password"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={pin}
                    onChange={onPinChange}
                    error={!!error?.pin}
                  />
                  <FieldError error={error?.pin && { message: error.pin }} mb={3} />
                </Box>
              )}
            </Flex>
            <FieldError error={error?.validation && { message: error.validation }} mb={3} />
            <Box mr={[0, 8]} my={4}>
              <Button
                disabled={isViiVoucherMaintenance || (!isViiVoucher && !voucherCode) || isLoading}
                onClick={applyVoucher}
                variant="primary"
                width={['100%', 'auto']}
                data-testid="apply-voucher-button"
              >
                Apply
              </Button>
            </Box>
            <VoucherTerms />
          </Flex>
        </Box>
      )}
    </Wrapper>
  );
});

HotelsVoucher.displayName = 'HotelsVoucher';

export default HotelsVoucher;
