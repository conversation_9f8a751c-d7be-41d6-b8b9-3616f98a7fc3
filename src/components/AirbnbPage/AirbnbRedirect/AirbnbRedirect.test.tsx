import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import AirbnbRedirect from './AirbnbRedirect';
import { searchAirbnb } from 'lib/clients/searchAirbnb';
import { getMemberId } from 'store/user/userSelectors';
import { useRouter } from 'next/router';
import { waitForComponent } from 'test-utils/enzymeUtils/mountUtils';

jest.mock('lib/clients/searchAirbnb');
jest.mock('store/user/userSelectors');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

const mockSearchResponse = { url: 'https://test-airbnb-redirect.com' };
const mockMemberId = '1968159226';
const mockRouter = {
  query: {
    location: 'Sydney, New South Wales, Australia',
    checkin: '2023-04-04',
    checkout: '2023-04-05',
    guests: 2,
  },
};

const decorators = { store: true, theme: true };
const render = () => mountUtils(<AirbnbRedirect />, { decorators });

describe('AirbnbRedirect', () => {
  beforeEach(() => {
    mocked(searchAirbnb).mockReturnValue(mockSearchResponse);
    mocked(getMemberId).mockReturnValue(mockMemberId);
    mocked(useRouter).mockReturnValue(mockRouter);
  });

  it('renders the airbnb and jetstar logos', async () => {
    const { wrapper, find } = render();
    await waitForComponent(wrapper);
    expect(find("[title='roo']")).toExist();
    expect(find("[title='airbnb']")).toExist();
  });

  it('renders the heading', async () => {
    const { wrapper, findByText } = render();
    await waitForComponent(wrapper);
    expect(findByText('Redirecting you to Airbnb')).toExist();
  });

  it('renders the body text', async () => {
    const { wrapper, findByText } = render();
    await waitForComponent(wrapper);
    expect(findByText('Complete your booking on Airbnb during this session to earn Qantas Points')).toExist();
  });

  it('calls the airbnb search endpoint correctly', async () => {
    const { wrapper } = render();
    await waitForComponent(wrapper);
    expect(searchAirbnb).toHaveBeenCalledWith({
      memberNumber: mockMemberId,
      location: 'Sydney, New South Wales, Australia',
      checkIn: '2023-04-04',
      checkOut: '2023-04-05',
      guests: 2,
    });
  });

  it('shows the redirect link with the correct url', async () => {
    const { wrapper, find } = render();
    await waitForComponent(wrapper);
    expect(find(`[href="https://test-airbnb-redirect.com"]`)).toExist();
  });
});
