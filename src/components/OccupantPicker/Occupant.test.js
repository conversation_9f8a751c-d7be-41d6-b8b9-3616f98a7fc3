import React from 'react';
import { mount } from 'enzyme';
import Occupant from './Occupant';

let wrapper;
let increment;
let decrement;
const label = 'Adults';
const ages = '2-11yrs';
const count = 1;

beforeEach(() => {
  increment = jest.fn();
  decrement = jest.fn();
  wrapper = mount(<Occupant label={label} count={count} min={0} max={2} increment={increment} decrement={decrement} />);
});

it('renders the label', () => {
  expect(wrapper.find('div[data-testid="occupant-label"]')).toHaveText(label);
});

describe('ages label', () => {
  describe('with no ages prop', () => {
    it('does not render ages', () => {
      wrapper = mount(<Occupant label={label} count={count} min={0} max={2} increment={increment} decrement={decrement} />);
      expect(wrapper.find('div[data-testid="occupant-ages"]')).toHaveLength(0);
    });
  });

  describe('with ages prop', () => {
    it('does not render ages', () => {
      wrapper = mount(<Occupant label={label} ages={ages} count={count} min={0} max={2} increment={increment} decrement={decrement} />);
      expect(wrapper.find('div[data-testid="occupant-ages"]')).toHaveText(ages);
    });
  });
});

it('renders the count', () => {
  expect(wrapper.find('div[data-testid="occupant-count"]').text()).toEqual(String(count));
});

it('calls the increment function when clicking increment action', () => {
  wrapper.find('button[data-testid="increment"]').simulate('click');
  expect(increment).toHaveBeenCalled();
});

it('calls the decrement function when clicking decrement action', () => {
  wrapper.find('button[data-testid="decrement"]').simulate('click');
  expect(decrement).toHaveBeenCalled();
});

it('hides the increment action when count is at the max', () => {
  wrapper = mount(<Occupant label={label} count={2} min={0} max={2} increment={increment} decrement={decrement} />);
  expect(wrapper.find('button[data-testid="increment"]').props()['disabled']).toBe(true);
  expect(wrapper.find('button[data-testid="decrement"]').props()['disabled']).toBe(false);
});

it('hides the decrement action when count is at the min', () => {
  wrapper = mount(<Occupant label={label} count={0} min={0} max={2} increment={increment} decrement={decrement} />);
  expect(wrapper.find('button[data-testid="increment"]').props()['disabled']).toBe(false);
  expect(wrapper.find('button[data-testid="decrement"]').props()['disabled']).toBe(true);
});
