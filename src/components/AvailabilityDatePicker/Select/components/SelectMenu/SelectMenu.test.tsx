import React from 'react';
import { mountUtils } from 'test-utils';
import SelectMenu from './SelectMenu';

const options = [{ value: 'option', text: 'Option' }];
const onMenuItemClick = jest.fn();

const render = () =>
  mountUtils(<SelectMenu options={options} menuLabel="menuLabel" onMenuItemClick={onMenuItemClick} />, {
    decorators: { store: true, theme: true },
  });

describe('<SelectMenu />', () => {
  it('renders the <MenuLabel> with the correct prop', () => {
    const { find, findByText } = render();
    expect(find('MenuLabel')).toExist();
    expect(findByText('menuLabel')).toExist();
  });

  it('renders the MenuItem with the correct prop', () => {
    const { find, findByText } = render();
    expect(find('MenuItem')).toExist();
    expect(findByText('Option')).toExist();
  });

  it('triggers onMenuItemClick callback', () => {
    const { find } = render();
    find('MenuItem').simulate('click');
    expect(onMenuItemClick).toHaveBeenCalled();
  });
});
