import React from 'react';
import { mountUtils } from 'test-utils';
import { useBreakpoints } from 'hooks/useBreakpoints';
import Select from './Select';
import SelectMenu from './components/SelectMenu';

jest.mock('./components/SelectMenu', () => jest.fn(() => 'SelectMenu'));
jest.mock('hooks/useBreakpoints');

const isLessThanBreakpoint = jest.fn();
const options = [{ value: 'option', text: 'Option' }];

const defaultProps = {
  options,
  placeholder: 'placeholder',
  onSelectedChange: jest.fn(),
  setShowMenu: jest.fn(),
  showMenu: false,
};

const render = () =>
  mountUtils(<Select {...defaultProps} />, {
    decorators: { store: true, theme: true },
  });

describe('<Select />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useBreakpoints.mockReturnValue({ isLessThanBreakpoint });
    isLessThanBreakpoint.mockReturnValue(true);
  });

  describe('when showMenu is TRUE', () => {
    it('renders the <Select> component', () => {
      const { findByTestId } = render((defaultProps.showMenu = true));
      expect(findByTestId('room-dropdown-select-fullscreen')).toExist();
    });

    it('renders the <SelectMenu> component with the correct props', () => {
      const { findByTestId } = render((defaultProps.showMenu = true));
      findByTestId('room-dropdown-select').simulate('click');
      expect(findByTestId('room-dropdown-select-fullscreen')).toExist();
      expect(SelectMenu).toHaveBeenCalledWith(
        {
          onCloseClick: expect.any(Function),
          onMenuItemClick: expect.any(Function),
          menuLabel: 'placeholder',
          options,
        },
        expect.anything(),
      );
    });
  });

  describe('when showMenu is FALSE', () => {
    it('does not render the <Select> component', () => {
      const { findByTestId } = render((defaultProps.showMenu = false));
      expect(findByTestId('room-dropdown-select-fullscreen')).not.toExist();
    });
  });
});
