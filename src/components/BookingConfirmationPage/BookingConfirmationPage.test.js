import React from 'react';
import BookingConfirmationPage from './BookingConfirmationPage';
import { mountUtils } from 'test-utils';
import { fetchBooking, trackBooking, clearBooking, showNpsSurveyAfterBooking } from 'store/booking/bookingActions';
import { getBooking } from 'store/booking/bookingSelectors';
import { useIsAuthenticated, useLogout } from 'lib/oauth';
import { useParams } from 'react-router';
import { useRouter } from 'next/router';
import * as config from 'config';
import { fetchFaqs } from 'store/faqs/faqActions';

jest.mock('@loadable/component', () => {
  const InnerLoadable = () => null;

  return (importFn) => {
    // Attach a chunkName method to importFn
    if (!importFn.chunkName) {
      importFn.chunkName = () => 'BookingConfirmationLayout';
    }
    return (props) => <InnerLoadable {...props} importFn={importFn} />;
  };
});
jest.mock('config');
jest.mock('react-router');
jest.mock('store/booking/bookingSelectors');
jest.mock('store/faqs/faqSelectors');
jest.mock('lib/oauth');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

afterEach(() => jest.clearAllMocks());

const bookingId = 'booking-1234';
const bookingPayload = { bookingId };

const render = (props) => mountUtils(<BookingConfirmationPage {...props} />, { decorators: { store: true } });

const props = {
  match: {
    params: {
      bookingId: 'booking-id',
    },
  },
};

const mockRouter = {
  query: {
    ss_action: 'rebook',
  },
};

const mockLogout = jest.fn();

describe('<BookingConfirmationPage />', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    config.QFF_ACCOUNT_MANAGEMENT = jest.requireActual('config').ACCOUNT_MANAGEMENT_TYPES.APP_WIDE;
    useParams.mockReturnValue(bookingPayload);
    useIsAuthenticated.mockReturnValue(false);
    useLogout.mockReturnValue({ logout: mockLogout });
    useRouter.mockReturnValue(mockRouter);
  });

  it('renders loadable <BookingConfirmationPage /> passing all props', () => {
    const { find } = render(props);

    expect(find('InnerLoadable').prop('importFn').chunkName()).toBe('BookingConfirmationLayout');

    expect(find('InnerLoadable')).toHaveProp(props);
  });

  it('fetches FAQs', () => {
    let decorators = render(props).decorators;
    expect(decorators.store.dispatch).toHaveBeenCalledWith(fetchFaqs());
  });

  describe('when the booking exists (coming from the checkout page)', () => {
    let decorators;

    beforeEach(() => {
      getBooking.mockReturnValue(bookingPayload);
      decorators = render(props).decorators;
    });

    it('does not fetch booking', () => {
      expect(decorators.store.dispatch).not.toHaveBeenCalledWith(fetchBooking('booking-id'));
    });

    it('dispatches trackBooking', () => {
      expect(decorators.store.dispatch).toHaveBeenCalledWith(trackBooking({ booking: bookingPayload, isRebooked: true, quote: undefined }));
    });

    it('dispatches showNpsSurveyAfterBooking', () => {
      expect(decorators.store.dispatch).toHaveBeenCalledWith(showNpsSurveyAfterBooking());
    });
  });

  describe('when the booking does not exist (hitting the page directly)', () => {
    let decorators;

    beforeEach(() => {
      getBooking.mockReturnValue({});
      decorators = render(props).decorators;
    });

    it('fetches booking', () => {
      expect(decorators.store.dispatch).toHaveBeenCalledWith(fetchBooking(bookingId));
    });

    it('does not dispatch trackBooking', () => {
      expect(decorators.store.dispatch).not.toHaveBeenCalledWith(trackBooking(bookingPayload));
    });

    it('does not dispatch showNpsSurveyAfterBooking', () => {
      expect(decorators.store.dispatch).not.toHaveBeenCalledWith(showNpsSurveyAfterBooking());
    });
  });

  describe('onExit', () => {
    it('clears the booking from the store', () => {
      const store = { dispatch: jest.fn() };
      BookingConfirmationPage.onExitPage({ store });

      expect(store.dispatch).toHaveBeenCalledWith(clearBooking());
    });
  });

  describe('when QFF_ACCOUNT_MANAGEMENT is checkout only', () => {
    beforeEach(() => {
      config.QFF_ACCOUNT_MANAGEMENT = jest.requireActual('config').ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY;
    });

    describe('when user is logged in', () => {
      it('logout QFF user is called', () => {
        useIsAuthenticated.mockReturnValue(true);
        render(props);
        expect(mockLogout).toHaveBeenCalled();
      });
    });

    describe('when user is logged out', () => {
      it('logout QFF user is NOT called', () => {
        useIsAuthenticated.mockReturnValue(false);
        render(props);
        expect(mockLogout).not.toHaveBeenCalled();
      });
    });
  });
});
