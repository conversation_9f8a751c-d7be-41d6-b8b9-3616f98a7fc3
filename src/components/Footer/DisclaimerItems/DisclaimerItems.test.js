import React from 'react';
import { mountUtils } from 'test-utils';
import { mount } from 'enzyme';
import { DisclaimerItemLogo, DisclaimerText, DisclaimerLink } from './DisclaimerItems';

jest.mock('store/ui/uiSelectors');

describe('<DisclaimerItemLogo />', () => {
  let props;

  const render = () => mount(<DisclaimerItemLogo {...props} />);

  beforeEach(() => {
    props = {};
  });

  it('renders real good', () => {
    expect(render).not.toThrow();
  });
});

describe('<DisclaimerText />', () => {
  let props;

  const render = () => mount(<DisclaimerText {...props} />);

  beforeEach(() => {
    props = {};
  });

  it('renders real good', () => {
    expect(render).not.toThrow();
  });
});

describe('<DisclaimerLink />', () => {
  let props;
  const decorators = { router: true, store: true, theme: true };

  const render = () => mountUtils(<DisclaimerLink {...props} />, { decorators });

  beforeEach(() => {
    props = {};
  });

  it('renders real good', () => {
    expect(render).not.toThrow();
  });
});
