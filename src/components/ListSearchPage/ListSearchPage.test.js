import React from 'react';
import { mountUtils } from 'test-utils';
import ListSearchPage from './ListSearchPage';
import { clearResults } from 'store/search/searchActions';

jest.mock('@loadable/component', () => {
  const InnerLoadable = () => null;
  return (importFn) => {
    // Attach a chunkName method to importFn
    if (!importFn.chunkName) {
      importFn.chunkName = () => 'ListSearchLayout';
    }
    return (props) => <InnerLoadable {...props} importFn={importFn} />;
  };
});

const render = (props) => mountUtils(<ListSearchPage {...props} />, { decorators: { store: true } });

it('renders loadable <ListSearchLayout /> passing all props', () => {
  const props = { a: 1, b: 2, c: 3 };
  const { find } = render(props);
  const chunkName = find('InnerLoadable').prop('importFn').chunkName();

  expect(chunkName).toBe('ListSearchLayout');
  expect(find('ListSearchPage')).toHaveProp(props);
});

it('dispatches clear search results on page navigation', () => {
  const store = { dispatch: jest.fn() };
  ListSearchPage.onExitPage({ store });
  expect(store.dispatch).toHaveBeenCalledWith(clearResults());
});
