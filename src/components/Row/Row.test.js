import React from 'react';
import { mountWithTheme } from 'test-utils';
import Row from './Row';

describe('<Row />', () => {
  const render = (props) =>
    mountWithTheme(
      <Row {...props}>
        <div>child one</div>
        <div>child two</div>
        <div>child three</div>
      </Row>,
    );

  it('works without a spacing prop', () => {
    expect(render).not.toThrow();
  });

  it('adds a negative margin with a string spacing prop', () => {
    const Wrapper = render({ spacing: '6px' });
    const Subject = Wrapper.getDOMNode();
    expect(Subject).toHaveStyle('margin-left: -6px');
    expect(Subject).toHaveStyle('margin-right: -6px');
  });

  it('adds margin to children with a string spacing prop', () => {
    const Wrapper = render({ spacing: '6px' });
    const Subject = Wrapper.getDOMNode().querySelector('div');
    expect(Subject).toHaveStyle('margin-left: 6px');
    expect(Subject).toHaveStyle('margin-right: 6px');
  });

  it('adds a negative margin with a number spacing prop', () => {
    const Wrapper = render({ spacing: 1 });
    const Subject = Wrapper.getDOMNode();
    expect(Subject).toHaveStyle('margin-left: -0.25rem');
    expect(Subject).toHaveStyle('margin-right: -0.25rem');
  });

  it('adds margin to children with a number spacing prop', () => {
    const Wrapper = render({ spacing: 1 });
    const Subject = Wrapper.getDOMNode().querySelector('div');
    expect(Subject).toHaveStyle('margin-left: 0.25rem');
    expect(Subject).toHaveStyle('margin-right: 0.25rem');
  });
});
