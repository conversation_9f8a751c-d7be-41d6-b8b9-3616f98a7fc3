import React from 'react';
import CancellationRefundModal from './CancellationRefundModal';
import { mountUtils } from 'test-utils';
import { useDataLayer } from 'hooks/useDataLayer';
import MockDate from 'mockdate';

MockDate.set('January 01, 2020');

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');

const nonRefundableCancellationPolicy = {
  isNonrefundable: true,
  description: 'Non Refundable',
  cancellationWindows: [],
};

const refundableCancellationPolicy = {
  isNonrefundable: false,
  description: 'Refundable',
  cancellationWindows: [
    {
      startTime: new Date('2020/01/01').toString(),
      formattedBeforeDate: 'Wed 1 Jan, 2020',
      nights: '1',
    },
  ],
};

const decorators = { theme: true, store: true };
const render = (props = {}) => mountUtils(<CancellationRefundModal {...props} />, { decorators });
const emitInteractionEvent = jest.fn();

beforeEach(() => {
  useDataLayer.mockReturnValue({ emitInteractionEvent });
});

it('passes the correct props to CancellationRefundSummary', () => {
  const props = {
    cancellationPolicy: nonRefundableCancellationPolicy,
    fontSize: [10, 18],
    hideBeforeDate: true,
    hideWhenNonRefundable: true,
  };
  const { find } = render(props);
  expect(find('CancellationRefundSummary')).toHaveProp({
    ...props,
    handleOnClick: expect.any(Function),
  });
});

describe('Clicking the link to trigger a modal', () => {
  it('Displays the cancellation policy description in a modal', () => {
    const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicy });
    findByTestId('cancellation-policy-message').simulate('click');
    const cancellationText = findByTestId('modal-cancellation-text');
    const cancellationPolicyText = `Free cancellation before ${refundableCancellationPolicy.cancellationWindows[0].formattedBeforeDate}`;
    expect(cancellationText).toHaveText(`Cancellation policy${cancellationPolicyText}${refundableCancellationPolicy.description}`);
  });

  it('Does not throw an error if there is no cancellation date', () => {
    const refundableCancellationPolicy2 = {
      isNonrefundable: false,
      description: 'Refundable',
      cancellationWindows: [{ startTime: refundableCancellationPolicy.cancellationWindows[0].startTime }],
    };
    expect(() =>
      render({ cancellationPolicy: refundableCancellationPolicy2 }).findByTestId('cancellation-policy-message').simulate('click'),
    ).not.toThrow();
  });

  it('Displays the non-refundable cancellation policy description in a modal', () => {
    const { findByTestId } = render({ cancellationPolicy: nonRefundableCancellationPolicy });
    findByTestId('cancellation-policy-message').simulate('click');
    const cancellationText = findByTestId('modal-cancellation-text');
    const cancellationPolicyText = 'Non-refundable';
    expect(cancellationText).toHaveText(`Cancellation policy${cancellationPolicyText}${nonRefundableCancellationPolicy.description}`);
  });

  it('dispatches an event to the data layer', () => {
    const { findByTestId } = render({ cancellationPolicy: refundableCancellationPolicy });
    findByTestId('cancellation-policy-message').simulate('click');
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Summary View',
      value: 'Cancellation Policy Selected',
    });
  });
});
