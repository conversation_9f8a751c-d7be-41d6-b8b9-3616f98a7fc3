import React from 'react';
import { mountUtils } from 'test-utils';
import MapSearchPage from './MapSearchPage';
import { clearResults } from 'store/search/searchActions';

jest.mock('@loadable/component', () => {
  const InnerLoadable = () => null;
  return (importFn) => {
    // Attach a chunkName method to importFn
    if (!importFn.chunkName) {
      importFn.chunkName = () => 'MapSearchLayout';
    }
    return (props) => <InnerLoadable {...props} importFn={importFn} />;
  };
});

const render = (props) => mountUtils(<MapSearchPage {...props} />);

it('renders loadable <MapSearchLayout /> passing all props', () => {
  const props = { a: 1, b: 2, c: 3 };
  const { find } = render(props);
  const chunkName = find('InnerLoadable').prop('importFn').chunkName();

  expect(chunkName).toBe('MapSearchLayout');
  expect(find('MapSearchPage')).toHaveProp(props);
});

it('dispatches clear search results on page navigation', () => {
  const store = { dispatch: jest.fn() };
  MapSearchPage.onExitPage({ store });

  expect(store.dispatch).toHaveBeenCalledWith(clearResults());
});
