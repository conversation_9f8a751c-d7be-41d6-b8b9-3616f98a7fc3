import React from 'react';
import { mountUtils } from 'test-utils';
import LoadingIndicator from './LoadingIndicator';
import theme from 'lib/theme';

describe('<LoadingIndicator />', () => {
  let props;

  const render = () => mountUtils(<LoadingIndicator {...props} />, { decorators: { theme: true } });

  beforeEach(() => {
    props = {};
  });

  it('renders without throwing', () => {
    expect(render).not.toThrow();
  });

  it('renders the correct background color', () => {
    const { findByTestId } = render();
    expect(findByTestId('wrapper')).toHaveStyleRule('background-color', theme.colors.greys.porcelain);
  });
});
