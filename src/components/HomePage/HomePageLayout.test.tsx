import React from 'react';
import HomePageLayout from './HomePageLayout';
import { mocked, mountUtils } from 'test-utils';
import {
  getHomePageContent,
  getFooterLinksByCategory,
  getTermsAndConditions,
  getFeaturedCampaigns,
} from 'store/homePage/homePageSelectors';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { Masonry } from 'react-masonry';

jest.mock('react-masonry', () => ({
  Masonry: jest.fn(),
}));

jest.mock('store/homePage/homePageSelectors');
jest.mock('store/ui/uiSelectors');

mountUtils.mockComponent('HomePageHelmet');
mountUtils.mockComponent('HomePageSearchPanel');
mountUtils.mockComponent('RequestCallbackModal');
mountUtils.mockComponent('ValuePropositionsList');
mountUtils.mockComponent('CrossSellList');
mountUtils.mockComponent('OffersList');
mountUtils.mockComponent('ListOfRegions');
mountUtils.mockComponent('PopularDestinationFooter');
mountUtils.mockComponent('Carousel');
mountUtils.mockComponent('TermsAndConditions');

const mockContent = {
  valuePropositions: [
    {
      title: 'value proposition 1',
      iconName: 'wine',
      description: 'description of value proposition 1',
    },
    {
      title: 'value proposition 2',
      iconName: 'wine',
      description: 'description of value proposition 2',
    },
  ],
  crossSells: {
    title: 'cross sell title',
    valuePropositions: [
      {
        title: 'value proposition 1',
        iconName: 'wine',
        description: 'description of value proposition 1',
        href: '/wine.html',
      },
      {
        title: 'value proposition 2',
        iconName: 'wine',
        description: 'description of value proposition 2',
        href: '/wine.html',
      },
    ],
  },
  regionLinks: [
    {
      title: 'list of links',
      links: [
        { name: 'region 1', fullName: 'region 1, state 1', id: 'r1' },
        { name: 'region 2', fullName: 'region 2, state 1', id: 'r2' },
      ],
    },
  ],
};

const mockFooterLinks = [
  {
    linkList: [
      {
        children: 'Sydney',
        href: '/hotels/search/list?location=Sydney, NSW, Australia',
        testId: 'sydney-nsw-australia',
        trackingName: 'Sydney, NSW, Australia',
      },
    ],
    title: 'New South Wales',
  },
];

const mockFeaturedCampaigns = ['campaign1', 'campaign2'];
const mockTermsConditions = [{ disclaimer: 'offer1' }, { disclaimer: 'offer2' }, { disclaimer: 'vpp1' }, { disclaimer: 'vpp2' }];

const decorators = { store: true, router: true, helmet: true };
const render = () => mountUtils(<HomePageLayout />, { decorators });

describe('HomePageLayout', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(getHomePageContent).mockReturnValue(mockContent);
    mocked(getFooterLinksByCategory).mockReturnValue(mockFooterLinks);
    mocked(getTermsAndConditions).mockReturnValue(mockTermsConditions);
    mocked(getFeaturedCampaigns).mockReturnValue(mockFeaturedCampaigns);
    mocked(getIsMobileApp).mockReturnValue(false);
    Masonry.mockImplementation(({ children }) => <div>{children}</div>);
  });

  it('renders HomePageHelmet', () => {
    const { find } = render();
    expect(find('HomePageHelmet')).toExist();
  });

  it('renders HomePageSearchPanel', () => {
    const { find } = render();
    expect(find('HomePageSearchPanel')).toExist();
  });

  it('renders RequestCallbackModal', () => {
    const { find } = render();
    expect(find('RequestCallbackModal')).toExist();
  });

  it('renders ValuePropositionsList', () => {
    const { find } = render();
    expect(find('ValuePropositionsList')).toExist();
  });

  // it('renders CrossSellList', () => {
  //   const { find } = render();
  //   expect(find('CrossSellList')).toExist();
  // });

  it('renders FeaturedOffersCarousel', () => {
    const { find } = render();
    expect(find('Carousel')).toExist();
  });

  it('renders OffersList', () => {
    const { find } = render();
    expect(find('OffersList')).toExist();
  });

  it('renders ListOfRegions with the correct title', () => {
    const { find } = render();
    expect(find('ListOfRegions')).toHaveProp({ title: mockContent.regionLinks.title });
  });

  it('renders PopularDestinationFooter', () => {
    const { find } = render();
    expect(find('PopularDestinationFooter')).toExist();
  });

  it('renders TermsAndConditions', () => {
    const { find } = render();
    expect(find('TermsAndConditions')).toExist();
  });

  it('does not throw an error if there is no homepage regions', () => {
    mocked(getHomePageContent).mockReturnValue(undefined);
    expect(render).not.toThrow();
  });

  describe('when user is using the Qantas Travel App', () => {
    beforeEach(() => {
      mocked(getIsMobileApp).mockReturnValue(true);
    });
    it('does NOT render anything below the fold', () => {
      const { find } = render();
      expect(find('ValuePropositionsList')).not.toExist();
      expect(find('FeaturedCampaignsCarousel')).not.toExist();
      expect(find('OffersList')).not.toExist();
      expect(find('ListOfRegions')).not.toExist();
      expect(find('PopularDestinationFooter')).not.toExist();
      expect(find('TermsAndConditions')).not.toExist();
    });
  });
});
