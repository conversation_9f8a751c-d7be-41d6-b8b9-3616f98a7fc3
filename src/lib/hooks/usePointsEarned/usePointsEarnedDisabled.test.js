import { renderHook } from 'test-utils';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import * as config from 'config';

jest.mock('config');
jest.mock('store/ui/uiSelectors');
jest.mock('lodash/debounce', () => (fn) => fn);

let usePointsEarned;

const initialPointsEarned = {
  maxQffEarnPpd: 3,
  maxQbrEarnPpd: 3,
  qffPoints: {
    base: 1000,
    total: 2000,
    qffPointsClub: 0,
  },
  qbrPoints: {
    total: 500,
  },
  propertyPpd: 3,
};

const render = (props = {}) => {
  return renderHook(() => usePointsEarned({ pointsEarned: initialPointsEarned, ...props }), { store: true });
};

beforeEach(() => {
  getIsPointsPay.mockReturnValue(true);
});

describe('with POINTS_EARN_ENABLED off', () => {
  beforeAll(() => {
    Object.assign(config, jest.requireActual('config'));
    config.POINTS_EARN_ENABLED = false;

    usePointsEarned = jest.requireActual('./usePointsEarned').default;
  });

  it('returns null for pointsEarned', () => {
    const { result } = render();
    expect(result.current.pointsEarned).toEqual(null);
  });

  it('returns a noop function for onCashPaymentAmountChange', () => {
    const { result } = render();
    expect(result.current.onCashPaymentAmountChange).toBeInstanceOf(Function);
    expect(result.current.onCashPaymentAmountChange).not.toThrow();
  });
});
