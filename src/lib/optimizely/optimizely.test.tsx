import { render, screen } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import { OptimizelyProviderWrapper } from './optimizely';
import { useRouter } from 'next/router';
import { mocked } from 'test-utils';
import { createInstance, OptimizelyProvider } from '@optimizely/react-sdk';
import { getQhUserId } from 'store/user/userSelectors';
import type { Store } from '@reduxjs/toolkit';
import getUserGroup from 'lib/getUserGroup';

const OPTIMIZELY_SDK_KEY = 'SdkKey123';
const USER_ID = 'User1234';
const USER_GROUP = 'selected-user-group';
const store = {
  dispatch: jest.fn(),
  getState: jest.fn().mockReturnValue({}),
  subscribe: jest.fn(),
} as unknown as Store;

jest.mock('@optimizely/react-sdk', () => ({
  createInstance: jest.fn(() => {
    return { user: { id: USER_ID } };
  }),
  OptimizelyProvider: jest.fn(({ children }) => <div data-testid="OPTIMIZELY_PROVIDER">{children}</div>),
  useDecision: jest.fn(),
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('store/user/userSelectors', () => ({
  getQhUserId: jest.fn(),
}));

jest.mock('config', () => ({
  OPTIMIZELY_DATAFILE: OPTIMIZELY_SDK_KEY,
}));

jest.mock('lib/getUserGroup');

describe('OptimizelyProviderWrapper', () => {
  beforeEach(() => {
    mocked(useRouter).mockReturnValue({
      isReady: true,
    });
    mocked(getQhUserId).mockReturnValue(USER_ID);
    mocked(getUserGroup).mockReturnValue(USER_GROUP);
  });

  afterEach(() => {
    mocked(useRouter).mockReset();
    mocked(getQhUserId).mockReset();
    mocked(getUserGroup).mockReset();
  });

  it('renders children', () => {
    render(
      <Provider store={store}>
        <OptimizelyProviderWrapper>
          <div data-testid="OPTIMIZELY_PROVIDER_CHILDREN" />
        </OptimizelyProviderWrapper>
      </Provider>,
    );

    expect(screen.getByTestId('OPTIMIZELY_PROVIDER_CHILDREN')).toBeInTheDocument();

    expect(OptimizelyProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        optimizely: { user: { id: USER_ID } },
        user: {
          attributes: {
            user_id: USER_ID,
            user_group: USER_GROUP,
          },
          id: USER_ID,
        },
      }),
      expect.anything(),
    );
  });

  it('it calls createInstance', () => {
    render(
      <Provider store={store}>
        <OptimizelyProviderWrapper>
          <div />
        </OptimizelyProviderWrapper>
      </Provider>,
    );
    expect(createInstance).toHaveBeenCalledWith({
      eventBatchSize: 100,
      eventFlushInterval: 3000,
      odpOptions: {
        disabled: true,
      },
      datafileOptions: {
        autoUpdate: true,
        updateInterval: 60000,
      },
      sdkKey: OPTIMIZELY_SDK_KEY,
    });
  });
});
