import wrappedAxios from './wrappedAxios';
import { removeMemberFavourite } from './removeMemberFavourite';

jest.mock('./wrappedAxios');
jest.mock('lib/errors', () => ({
  throwWrappedAxiosError: jest.fn(),
}));

jest.mock('config', () => ({
  HOTELS_API_URL: '/hotelsApiUrl',
}));

const accessToken = 'accessToken';
const memberId = 'memberId';
const qhUserId = 'qhUserId';
const favouriteId = 'favouriteId';

beforeEach(() => {
  jest.clearAllMocks();
});

describe('with a successful response', () => {
  const mockResponse = { status: 204, data: { success: true } };

  beforeEach(() => {
    wrappedAxios.mockResolvedValue(mockResponse);
  });

  it('calls wrappedAxios with expected arguments', async () => {
    await removeMemberFavourite({ accessToken, favouriteId, memberId, qhUserId });

    expect(wrappedAxios).toHaveBeenCalledWith({
      url: `/hotelsApiUrl/members/${memberId}/favourites/${favouriteId}/delete`,
      method: 'POST',
      data: {},
      accessToken,
      qhUserId,
    });
  });

  it('returns the response status', async () => {
    const response = await removeMemberFavourite({ accessToken, favouriteId, memberId, qhUserId });

    expect(response).toEqual({ success: true });
  });
});

describe('with an unsuccessful response', () => {
  const error = new Error('error');

  beforeEach(() => {
    wrappedAxios.mockImplementation(() => Promise.reject(error));
  });

  it('throws an wrappedAxios error', async () => {
    await expect(removeMemberFavourite({ favouriteId, memberId, qhUserId })).rejects.toThrow(error);
  });
});
