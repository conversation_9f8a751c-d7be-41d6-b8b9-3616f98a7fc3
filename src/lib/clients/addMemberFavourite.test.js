import wrappedAxios from './wrappedAxios';
import { addMemberFavourite } from './addMemberFavourite';

jest.mock('./wrappedAxios');
jest.mock('lib/errors', () => ({
  throwWrappedAxiosError: jest.fn(),
}));

jest.mock('config', () => ({
  HOTELS_API_URL: '/hotelsApiUrl',
}));

const accessToken = 'accessToken';
const favourite = {
  propertyId: 'propertyId',
};
const memberId = 'memberId';
const qhUserId = 'qhUserId';

beforeEach(() => {
  jest.clearAllMocks();
});

describe('with a successful response', () => {
  const mockResponse = {
    data: {
      propertyId: 'propertyId',
    },
  };

  beforeEach(() => {
    wrappedAxios.mockResolvedValue(mockResponse);
  });

  it('calls wrappedAxios with expected arguments', async () => {
    await addMemberFavourite({ accessToken, favourite, memberId, qhUserId });

    expect(wrappedAxios).toHaveBeenCalledWith({
      url: `/hotelsApiUrl/members/${memberId}/favourites`,
      data: favourite,
      method: 'POST',
      accessToken,
      qhUserId,
    });
  });

  it('returns the response data', async () => {
    const response = await addMemberFavourite({ accessToken, favourite, memberId, qhUserId });

    expect(response).toEqual(mockResponse.data);
  });
});

describe('with an unsuccessful response', () => {
  const error = new Error('error');

  beforeEach(() => {
    wrappedAxios.mockImplementation(() => Promise.reject(error));
  });

  it('throws an wrappedAxios error', async () => {
    await expect(addMemberFavourite({ accessToken, favourite, memberId, qhUserId })).rejects.toThrow(error);
  });
});
