import wrappedAxios from './wrappedAxios';
import { fetchMerchandise } from './fetchMerchandise';

jest.mock('./wrappedAxios');
jest.mock('lib/errors', () => ({
  throwWrappedAxiosError: jest.fn(),
}));

jest.mock('config', () => ({
  HOTELS_API_URL: '/hotelsApiUrl',
}));

const limit = 10;
const regionId = 51234;
const subRegionsLimit = 5;

beforeEach(() => {
  jest.clearAllMocks();
});

describe('fetchMerchandise()', () => {
  describe('with a successful response', () => {
    beforeEach(() => {
      wrappedAxios.mockResolvedValue({
        data: {
          merchandising: 'merchandising',
        },
      });
    });

    it('calls wrappedAxios with expected arguments', async () => {
      await fetchMerchandise({ regionId, limit, subRegionsLimit });

      expect(wrappedAxios).toHaveBeenCalledWith({
        url: `/hotelsApiUrl/merchandising`,
        method: 'GET',
        params: {
          limit,
          regionId,
          subRegionsLimit,
        },
      });
    });

    it('returns the response data', async () => {
      const response = await fetchMerchandise({ regionId, limit, subRegionsLimit });

      expect(response).toEqual({
        merchandising: 'merchandising',
      });
    });
  });

  describe('with an unsuccessful response', () => {
    const error = new Error('error');

    beforeEach(() => {
      wrappedAxios.mockImplementation(() => Promise.reject(error));
    });

    it('throws an wrappedAxios error', async () => {
      await expect(fetchMerchandise({ regionId, limit, subRegionsLimit })).rejects.toThrow(error);
    });
  });
});
