import wrappedAxios from './wrappedAxios';
import { fetchAppVersion } from './fetchAppVersion';

jest.mock('./wrappedAxios');
jest.mock('lib/errors', () => ({
  throwWrappedAxiosError: jest.fn(),
}));

jest.mock('config', () => ({
  PUBLIC_PATH: 'publicPath/',
}));

let responseData;

describe('with a successful response', () => {
  beforeEach(() => {
    responseData = { hash: 'hash', version: 'version' };
    wrappedAxios.mockReturnValue(Promise.resolve({ data: responseData }));
  });

  it('calls wrappedAxios with expected arguments', async () => {
    await fetchAppVersion();
    expect(wrappedAxios).toHaveBeenCalledWith({
      url: `publicPath/appVersion.json`,
      method: 'get',
    });
  });

  it('returns the version', async () => {
    const response = await fetchAppVersion();
    expect(response).toEqual(responseData.version);
  });
});

describe('with an unsuccessful response', () => {
  const error = new Error('error');

  beforeEach(() => {
    wrappedAxios.mockRejectedValue(error);
  });

  it('throws a wrapped wrappedAxios error', async () => {
    await expect(fetchAppVersion).rejects.toThrow(error);
  });
});
