import wrappedAxios from './wrappedAxios';
import checkGeolocation from './checkGeolocation';

jest.mock('./wrappedAxios');
jest.mock('lib/errors', () => ({
  throwWrappedwrappedAxiosError: jest.fn(),
}));

jest.mock('config', () => ({
  HOTELS_API_URL: '/hotelsApiUrl',
}));

let responseData = { geolocation: 'NSW' };

describe('checkGeolocation()', () => {
  describe('with a successful response', () => {
    beforeEach(() => {
      wrappedAxios.mockImplementation(() => Promise.resolve({ data: responseData }));
    });

    it('calls wrappedAxios with expected arguments converted to correct types', async () => {
      await checkGeolocation();

      expect(wrappedAxios).toHaveBeenCalledWith({
        url: '/hotelsApiUrl/geolocation',
        method: 'get',
      });
    });

    it('returns the response data', async () => {
      const response = await checkGeolocation();
      expect(response).toEqual(responseData);
    });
  });

  describe('with an unsuccessful response', () => {
    const error = new Error('error');

    beforeEach(() => {
      wrappedAxios.mockImplementation(() => Promise.reject(error));
    });

    it('throws an wrappedAxios error', async () => {
      expect.assertions(1);
      await checkGeolocation().catch((e) => {
        expect(e).toEqual(error);
      });
    });
  });
});
