import { fetchSearchResultsSuccess } from 'store/search/searchActions';
import { getPageFromState } from './helpers';
import { getQueryParams } from 'store/router/routerSelectors';
import { createViewSearchResultsEvent, createHotelsViewSearchResultsEventPayload } from '@qantasexperiences/analytics';

const australiaStateNames = {
  NSW: 'New South Wales',
  QLD: 'Queensland',
  VIC: 'Victoria',
  WA: 'Western Australia',
  SA: 'South Australia',
  TAS: 'Tasmania',
  ACT: 'Australian Capital Territory',
  NT: 'Northern Territory',
};

const isRegionInLocation = (location, regionCode, regionLongName) => {
  const locationParts = location.split(',').map((part) => part.trim());
  return locationParts.includes(regionCode) || locationParts.includes(regionLongName);
};

const emitFetchSearchResultsSuccessEvent = (action, prevState) => {
  const meta = action.payload.meta;
  const page = getPageFromState(prevState);
  const queryParams = getQueryParams(prevState);

  const availablePropertyCount = meta?.counts?.filtered || 0;
  const totalPropertyCount = meta?.counts?.total || 0;
  const viewMode = page.name === 'JQ Search Page' ? 'list' : 'map';
  const searchLocation = queryParams.location;

  const destinationType = searchLocation.includes('Australia') ? 'Domestic' : 'International';
  const userCountry = action.payload.geolocation?.country;
  let destinationDomesticType;

  if (destinationType === 'Domestic' && userCountry === 'AU') {
    const userRegion = action.payload.geolocation.region;
    const userRegionLongNames = australiaStateNames[userRegion] || userRegion;
    destinationDomesticType = isRegionInLocation(searchLocation, userRegion, userRegionLongNames) ? 'Intrastate' : 'Interstate';
  }

  const viewSearchResultsEvent = createViewSearchResultsEvent(
    createHotelsViewSearchResultsEventPayload({
      availablePropertyCount,
      destinationDomesticType,
      destinationName: queryParams.location,
      destinationType,
      endDate: queryParams.checkOut,
      paymentType: meta.query.payWith,
      searchCategory: 'Hotels',
      searchTerm: queryParams.location,
      startDate: queryParams.checkIn,
      totalPropertyCount,
      viewMode,
      adults: Number(queryParams.adults),
      children: Number(queryParams.children),
      infants: Number(queryParams.infants),
    }),
  );

  return viewSearchResultsEvent;
};

export default { [fetchSearchResultsSuccess]: emitFetchSearchResultsSuccessEvent };
