import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { fetchSearchResultsSuccess } from 'store/search/searchActions';
import emitFetchSearchResultsSuccessEvent from './emitFetchSearchResultsSuccessEvent';
import { getQueryParams } from 'store/router/routerSelectors';
import { getPageFromState } from './helpers';
import { TRACKING_PREFIX } from 'config';
import checkGeolocation from 'lib/clients/checkGeolocation';

jest.mock('lib/clients/checkGeolocation');
jest.mock('store/router/routerSelectors');
jest.mock('./helpers');

let store;

const middleware = createMiddleware(emitFetchSearchResultsSuccessEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware]);
const createStore = (initialState) => mockStore(initialState);

describe('#emitFetchSearchResultsSuccessEvent', () => {
  let initialState;
  const queryParams = {
    location: 'Melbourne, VIC, Australia',
    checkIn: '2018-12-06',
    checkOut: '2018-12-12',
    adults: 1,
    children: 2,
    infants: 3,
  };
  const results = [{ property: { name: 'Property Name' } }];
  const meta = {
    counts: {
      filtered: 123,
      total: 456,
    },
    query: {
      payWith: 'cash',
    },
  };
  const geolocation = { region: 'NSW', country: 'AU' };

  beforeEach(() => {
    getQueryParams.mockReturnValue(queryParams);
    getPageFromState.mockReturnValue({ name: `${TRACKING_PREFIX} Search Page` });
    checkGeolocation.mockReturnValue({ region: 'NSW', country: 'AU' });
    initialState = {
      router: {
        action: 'push',
        location: {
          pathname: '/search/list',
        },
      },
    };

    window.dataLayer = [];
    store = createStore(initialState);
  });

  it('fires with the correct data', () => {
    store.dispatch(fetchSearchResultsSuccess({ results, meta, geolocation }));

    expect(window.dataLayer[0]).toStrictEqual({
      event: 'view_search_results',
      event_data: {
        action: 'view',
        available_property_count: 123,
        component_type: 'search_results',
        destination_domestic_type: 'Interstate',
        destination_name: 'Melbourne, VIC, Australia',
        destination_type: 'Domestic',
        end_date: queryParams.checkOut,
        payment_type: 'cash',
        search_category: 'Hotels',
        search_term: queryParams.location,
        start_date: queryParams.checkIn,
        total_property_count: 456,
        view_mode: 'list',
        travellers_adult: queryParams.adults,
        travellers_children: queryParams.children,
        travellers_infant: queryParams.infants,
      },
    });
  });

  it('fires with the correct data for an Intrastate search (long region name in location)', () => {
    const longStateQueryParams = {
      ...queryParams,
      location: 'Launceston, Tasmania, Australia',
    };
    const tasGeolocation = { region: 'TAS', country: 'AU' };

    getQueryParams.mockReturnValue(longStateQueryParams);
    checkGeolocation.mockReturnValue(tasGeolocation);

    store = createStore(initialState);
    store.dispatch(fetchSearchResultsSuccess({ results, meta, geolocation: tasGeolocation }));

    expect(window.dataLayer[0]).toStrictEqual({
      event: 'view_search_results',
      event_data: {
        action: 'view',
        available_property_count: 123,
        component_type: 'search_results',
        destination_domestic_type: 'Intrastate',
        destination_name: 'Launceston, Tasmania, Australia',
        destination_type: 'Domestic',
        end_date: queryParams.checkOut,
        payment_type: 'cash',
        search_category: 'Hotels',
        search_term: longStateQueryParams.location,
        start_date: queryParams.checkIn,
        total_property_count: 456,
        view_mode: 'list',
        travellers_adult: queryParams.adults,
        travellers_children: queryParams.children,
        travellers_infant: queryParams.infants,
      },
    });
  });

  it('fires with the correct data for an Intrastate search (short region name in location)', () => {
    const vicQueryParams = {
      ...queryParams,
      location: 'Melbourne, VIC, Australia',
    };
    const vicGeolocation = { region: 'VIC', country: 'AU' };

    getQueryParams.mockReturnValue(vicQueryParams);
    checkGeolocation.mockReturnValue(vicGeolocation);

    store = createStore(initialState);
    store.dispatch(fetchSearchResultsSuccess({ results, meta, geolocation: vicGeolocation }));

    expect(window.dataLayer[0]).toStrictEqual({
      event: 'view_search_results',
      event_data: {
        action: 'view',
        available_property_count: 123,
        component_type: 'search_results',
        destination_domestic_type: 'Intrastate',
        destination_name: 'Melbourne, VIC, Australia',
        destination_type: 'Domestic',
        end_date: queryParams.checkOut,
        payment_type: 'cash',
        search_category: 'Hotels',
        search_term: vicQueryParams.location,
        start_date: queryParams.checkIn,
        total_property_count: 456,
        view_mode: 'list',
        travellers_adult: queryParams.adults,
        travellers_children: queryParams.children,
        travellers_infant: queryParams.infants,
      },
    });
  });

  it('correctly identifies an Interstate search when the location name includes the region as a substring', () => {
    const vicUserQueryParams = {
      ...queryParams,
      location: 'Victoria Street, Sydney, NSW, Australia',
    };
    const vicGeolocation = { region: 'VIC', country: 'AU' };

    getQueryParams.mockReturnValue(vicUserQueryParams);
    checkGeolocation.mockReturnValue(vicGeolocation);
    store = createStore(initialState);
    store.dispatch(fetchSearchResultsSuccess({ results, meta, geolocation: vicGeolocation }));

    expect(window.dataLayer[0].event_data.destination_domestic_type).toStrictEqual('Interstate');
  });

  it('correctly identifies an Intrastate search when the location includes the long region name', () => {
    const nswUserQueryParams = {
      ...queryParams,
      location: 'Bondi Junction, New South Wales, Australia',
    };
    const nswGeolocation = { region: 'NSW', country: 'AU' };

    getQueryParams.mockReturnValue(nswUserQueryParams);
    checkGeolocation.mockReturnValue(nswGeolocation);
    store = createStore(initialState);
    store.dispatch(fetchSearchResultsSuccess({ results, meta, geolocation: nswGeolocation }));

    expect(window.dataLayer[0].event_data.destination_domestic_type).toStrictEqual('Intrastate');
  });

  it('fires with the correct data when no geolocation is provided', () => {
    const vicQueryParams = {
      ...queryParams,
      location: 'Melbourne, VIC, Australia',
    };
    const vicGeolocation = {};

    getQueryParams.mockReturnValue(vicQueryParams);
    checkGeolocation.mockReturnValue(vicGeolocation);

    store = createStore(initialState);
    store.dispatch(fetchSearchResultsSuccess({ results, meta, geolocation: vicGeolocation }));

    expect(window.dataLayer[0]).toStrictEqual({
      event: 'view_search_results',
      event_data: {
        action: 'view',
        available_property_count: 123,
        component_type: 'search_results',
        destination_name: 'Melbourne, VIC, Australia',
        destination_type: 'Domestic',
        end_date: queryParams.checkOut,
        payment_type: 'cash',
        search_category: 'Hotels',
        search_term: vicQueryParams.location,
        start_date: queryParams.checkIn,
        total_property_count: 456,
        view_mode: 'list',
        travellers_adult: queryParams.adults,
        travellers_children: queryParams.children,
        travellers_infant: queryParams.infants,
      },
    });
  });

  it('fires with the correct data when geolocation is not in Australia', () => {
    const vicQueryParams = {
      ...queryParams,
      location: 'Melbourne, VIC, Australia',
    };
    const americaGeolocation = { region: 'LAX', country: 'USA' };

    getQueryParams.mockReturnValue(vicQueryParams);
    checkGeolocation.mockReturnValue(americaGeolocation);

    store = createStore(initialState);
    store.dispatch(fetchSearchResultsSuccess({ results, meta, geolocation: americaGeolocation }));

    expect(window.dataLayer[0]).toStrictEqual({
      event: 'view_search_results',
      event_data: {
        action: 'view',
        available_property_count: 123,
        component_type: 'search_results',
        destination_name: 'Melbourne, VIC, Australia',
        destination_type: 'Domestic',
        end_date: queryParams.checkOut,
        payment_type: 'cash',
        search_category: 'Hotels',
        search_term: vicQueryParams.location,
        start_date: queryParams.checkIn,
        total_property_count: 456,
        view_mode: 'list',
        travellers_adult: queryParams.adults,
        travellers_children: queryParams.children,
        travellers_infant: queryParams.infants,
      },
    });
  });

  it('fires with the correct data when on the map page', () => {
    const vicQueryParams = {
      ...queryParams,
      location: 'Melbourne, VIC, Australia',
    };
    const vicGeolocation = { region: 'VIC', country: 'AU' };

    getPageFromState.mockReturnValue({ name: `${TRACKING_PREFIX} Map Page` });

    getQueryParams.mockReturnValue(vicQueryParams);
    checkGeolocation.mockReturnValue(vicGeolocation);

    store = createStore(initialState);
    store.dispatch(fetchSearchResultsSuccess({ results, meta, geolocation: vicGeolocation }));

    expect(window.dataLayer[0]).toStrictEqual({
      event: 'view_search_results',
      event_data: {
        action: 'view',
        available_property_count: 123,
        component_type: 'search_results',
        destination_domestic_type: 'Intrastate',
        destination_name: 'Melbourne, VIC, Australia',
        destination_type: 'Domestic',
        end_date: queryParams.checkOut,
        payment_type: 'cash',
        search_category: 'Hotels',
        search_term: vicQueryParams.location,
        start_date: queryParams.checkIn,
        total_property_count: 456,
        view_mode: 'map',
        travellers_adult: queryParams.adults,
        travellers_children: queryParams.children,
        travellers_infant: queryParams.infants,
      },
    });
  });
});
