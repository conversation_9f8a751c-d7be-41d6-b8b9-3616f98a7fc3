#!/usr/bin/env bash
set -e
cd "$(dirname $0)/../.."

function help () {
  echo "Usage: $0 [options] [ComponentName]"
  echo ""
  echo "Options:"
  echo "  --page    Generate component in src/pages/"
  echo ""
  echo "Examples:"
  echo "  $0 --page Search/PayWithToggle"
}

if [ -z "$1" ]; then
  help
  exit 1
fi

if [ "$1" = "--page" ]; then
  shift
  COMPONENT_DIR="src/pages/$1"
else
  COMPONENT_DIR="src/components/$1"
fi

COMPONENT=`basename $1`
COMPONENT_INDEX="$COMPONENT_DIR/index.js"
COMPONENT_JS="$COMPONENT_DIR/$COMPONENT.js"
COMPONENT_TEST="$COMPONENT_DIR/$COMPONENT.test.js"
COMPONENT_FIXTURE="$COMPONENT_DIR/$COMPONENT.fixture.js"

if [ -e "$COMPONENT_JS" ]; then
    echo "$COMPONENT_JS already exists"
    exit 1
fi

mkdir -p `dirname $COMPONENT_INDEX`


cat <<EOT > "$COMPONENT_INDEX"
export { default } from './$COMPONENT';
EOT

echo "Created $COMPONENT_INDEX"


cat <<EOT > "$COMPONENT_JS"
import React from 'react';

const $COMPONENT = () => <div>Default $COMPONENT</div>;

export default $COMPONENT;
EOT

echo "Created $COMPONENT_JS"


cat <<EOT > "$COMPONENT_TEST"
import React from 'react';
import { mount } from 'enzyme';
import $COMPONENT from './$COMPONENT';

describe('<$COMPONENT />', () => {
  let props;

  const render = () => mount(<$COMPONENT {...props} />);

  beforeEach(() => {
    props = {};
  });

  it('renders real good', () => {
    expect(render).not.toThrow();
  });
});

EOT

echo "Created $COMPONENT_TEST"


cat <<EOT > "$COMPONENT_FIXTURE"
import $COMPONENT from './$COMPONENT';

export default [
  {
    component: $COMPONENT,
    name: 'Default $COMPONENT',
    props: {},
  },
];
EOT

echo "Created $COMPONENT_FIXTURE"
