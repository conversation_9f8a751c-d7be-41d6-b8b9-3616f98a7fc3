#!/bin/bash

echo "--- Running \"$@\""

# Handle the command whether it's wrapped in /bin/sh -e -c or not
if [[ "$1" == "/bin/sh" && "$2" == "-e" && "$3" == "-c" ]]; then
  # Extract the actual command from the shell wrapper
  echo "--- Detected shell wrapper, extracting actual command"
  ACTUAL_COMMAND="${@:4}"
  echo "--- Executing: $ACTUAL_COMMAND"
  eval "$ACTUAL_COMMAND"
else
  # Execute the command directly
  echo "--- Executing direct command"
  eval "$@"
fi

EXIT_CODE=$?

exit $EXIT_CODE
