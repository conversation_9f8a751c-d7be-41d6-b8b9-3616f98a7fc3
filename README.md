[![Build status](https://badge.buildkite.com/5901a37d8755a28a4d7b5e94e372a6956eeba6291adb474687.svg)](https://buildkite.com/hooroo/jetstar-hotels-ui)

# Jetstar Hotels UI

## Contents

- [Getting Started](#prerequisites)
- [Installation](#installation)
- [Development](#development)
- [Deployed Environments](https://github.com/hooroo/jetstar-hotels-ui/wiki#environments)
- [FAQs](#faqs)
- [Contributing](https://github.com/hooroo/jetstar-hotels-ui/blob/master/.github/CONTRIBUTING.md)

## Getting Started

### Prerequisites

Please ensure that the following software packages are properly installed on your computer:

- [Git](https://git-scm.com/)
- [Node.js 22.x](https://nodejs.org/)
- [Node Version Manager](https://github.com/nvm-sh/nvm/)
- [Yarn](https://yarnpkg.com/)

### Installation

- `git clone https://github.com/hooroo/jetstar-hotels-ui`
- `cd jetstar-hotels-ui`
- `nvm install`
- `nvm use`
- `yarn install`

## Development

To run locally, run `yarn start` & navigate to http://localhost:3000.

### Running Unit Tests

There are two unit test suites to run. One for the running the app in a DOM context, and one for running in a server context.

```bash
yarn run test --watch
yarn run test:server --watch
```

### Running Smoke Tests

Smoke tests are run in Chrome using [Cypress](https://docs.cypress.io).

```bash
# Run tests against staging in headed mode in the CLI
yarn run test:e2e

# Run tests against http://localhost:3000 in GUI mode
yarn run test:e2e:dev
```

### Accessibility

We need to achieve [AA Conformance to Web Content Accessibility Guidelines 2.0](https://www.w3.org/WAI/WCAG21/quickref/?versions=2.0)

- You can test your components with the help of `jest-axe`
- In development we display warnings in the console with the help of `axe-core/react`
- A11Y issues are automatically checked for during the linting process using `eslint-plugin-jsx-a11y`

### Responsive UI

SSR (Server-side rendering) requires a CSS-only media-query approach.

- Breakpoints are set in `/lib/theme.js`
- [Styled System style utilities](https://github.com/jxnblk/styled-system/blob/master/docs/responsive-styles.md) use props that accept arrays as values for mobile-first responsive styles
- To hide and show based on screensize use the [`<Hide>` component](https://hooroo.github.io/roo-ui/?knob-Assets=static%2Fmedia%2Froo.0b6134e0.svg&selectedKind=Components%7CHide&selectedStory=default&full=0&addons=1&stories=1&panelRight=1&addonPanel=storybooks%2Fstorybook-addon-knobs) from Roo-UI

### Code Generators

- To generate a new component, run:

  ```bash
  # If you encounter a "permissions denied" error, resolve it by changing the permissions on the file.
  # Simply run the following command:
  # chmod +x ./scripts/dev/generate_component

  ./scripts/dev/generate_component MyNewComponent
  ```

## FAQs

#### Why set `--max-http-header-size=16000`?

Cookies on www.jetstar.com are currently causing some issues. During internal testing, it was found that the default max header size of 8KB in Node was being exceeded. To address this, the limit has been increased to 16KB, which aligns with the AWS ALB limit. Once the migration to new hotels is finished, there might be a chance to remove unnecessary cookies.
