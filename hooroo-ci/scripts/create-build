#!/usr/bin/env bash
set -eux

# Get the correct role for secrets manager
if [ "$ENVIRONMENT" == "production" ]; then
    ROLE="arn:aws:iam::801491100242:role/production-secrets-manager-role"
else
    ROLE="arn:aws:iam::549119245188:role/nonprod-secrets-manager-role"
fi

# Assume the role and get temporary credentials
CREDENTIALS=$(aws sts assume-role --role-arn $ROLE --role-session-name $ENVIRONMENT)

# Extract the temporary credentials using --query and store them in variables
SM_ACCESS_KEY_ID=$(echo "$CREDENTIALS" | jq -r '.Credentials.AccessKeyId')
SM_SECRET_ACCESS_KEY=$(echo "$CREDENTIALS" | jq -r '.Credentials.SecretAccessKey')
SM_SESSION_TOKEN=$(echo "$CREDENTIALS" | jq -r '.Credentials.SessionToken')
SM_REGION="ap-southeast-2"

# Configure AWS CLI with temporary credentials
aws configure set aws_access_key_id "$SM_ACCESS_KEY_ID" --profile "$ENVIRONMENT"
aws configure set aws_secret_access_key "$SM_SECRET_ACCESS_KEY" --profile "$ENVIRONMENT"
aws configure set aws_session_token "$SM_SESSION_TOKEN" --profile "$ENVIRONMENT"
aws configure set region "$SM_REGION" --profile "$ENVIRONMENT"

# Query Secrets Manager and store the output in .env file
aws secretsmanager get-secret-value --secret-id "ci/$ENVIRONMENT/$APP_NAME" \
    --profile "$ENVIRONMENT" \
    | jq -r '.SecretString' \
    | jq -r 'to_entries | map("\(.key)=\(.value)") | .[]' \
    > /application/.env

VERSION="jetstar-hotels-ui-$BUILDKITE_BUILD_NUMBER-$ENVIRONMENT"

# Build app
yarn run build

# Write out the REVISION
echo "$BUILDKITE_COMMIT" > "./build/GIT_REVISION"
echo "$BUILDKITE_BUILD_NUMBER" > "./build/BUILD_NUMBER"

echo "👋  Creating sentry release $VERSION..."
export SENTRY_PROJECT=jetstar-hotels-ui
yarn sentry-cli releases new "$VERSION"
yarn sentry-cli releases files "$VERSION" upload-sourcemaps ./build/static/ --rewrite --strip-common-prefix --validate --url-prefix "~"
yarn sentry-cli releases finalize "$VERSION"

# Remove cache to decrease build size
rm -rf build/cache

#Remove secrets
rm -f /application/.env

# Copy the public assets
cp -r public build/public

# Move build to build.env
tar -C build -czvf builds/${ENVIRONMENT}.tar.gz .
