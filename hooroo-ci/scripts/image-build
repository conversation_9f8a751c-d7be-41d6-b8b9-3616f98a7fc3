#!/usr/bin/env bash
set -eou pipefail

IMAGE=$REPO_URL/$REPO_NAME

: ${BUILDKITE_BUILD_NUMBER='local-dev'}

if [ -n "${ENVIRONMENT:-}" ]; then
  CACHE_TAG="${ENVIRONMENT}"
else
  CACHE_TAG="${BUILDKITE_BUILD_NUMBER}"
fi

# BUILDKITE_ARTIFACT_DOWNLOAD_PATHS is set by Buildkite

if [ -n "${BUILDKITE_ARTIFACT_DOWNLOAD_PATHS:-}" ]; then
  ARTIFACT_ARCHIVE="${BUILDKITE_ARTIFACT_DOWNLOAD_PATHS}"
  ARTIFACT_NAME="${ARTIFACT_ARCHIVE%.tar.gz}"

  echo "BUILDKITE_ARTIFACT_DOWNLOAD_PATHS is set to: $ARTIFACT_ARCHIVE"
  echo "Attempting to extract artifact from: $ARTIFACT_ARCHIVE"

  if [ -f "$ARTIFACT_ARCHIVE" ]; then
    echo "Artifact file found. Extracting into $ARTIFACT_NAME..."
    mkdir -p "$ARTIFACT_NAME" || true
    tar -xvzf "$ARTIFACT_ARCHIVE" -C "$ARTIFACT_NAME"
    echo "Artifact extraction complete."
  else
    echo "Error: Artifact specified by BUILDKITE_ARTIFACT_DOWNLOAD_PATHS ($ARTIFACT_ARCHIVE) not found." >&2
    exit 1
  fi
else
  echo "BUILDKITE_ARTIFACT_DOWNLOAD_PATHS not set. Skipping artifact extraction."
fi

###

RUNTIME_DOCKERFILE_PATH=""

if [ -n "${RUNTIME_DOCKERFILE:-}" ]; then
  if [ -f "${RUNTIME_DOCKERFILE}" ]; then
    RUNTIME_DOCKERFILE_PATH="${RUNTIME_DOCKERFILE}"
    echo "Using Dockerfile path from RUNTIME_DOCKERFILE env var: ${RUNTIME_DOCKERFILE_PATH}"
  else
    echo "Warning: Dockerfile specified by RUNTIME_DOCKERFILE ('${RUNTIME_DOCKERFILE}') does not exist. Docker build will use 'Dockerfile' in the current context." >&2
  fi
else
  echo "RUNTIME_DOCKERFILE env var not set. Docker build will use 'Dockerfile' in the current context."
fi

echo "Final Dockerfile path to use: ${RUNTIME_DOCKERFILE_PATH:-<none - will use default 'Dockerfile'>}"

DOCKERFILE_FLAG=""
if [ -n "${RUNTIME_DOCKERFILE_PATH}" ]; then
  DOCKERFILE_FLAG="-f $RUNTIME_DOCKERFILE_PATH"
fi

FINAL_FULL_IMAGE_TAG="${IMAGE_TAG:-$IMAGE:$BUILDKITE_BUILD_NUMBER}"

echo "Building Docker image with tag: $FINAL_FULL_IMAGE_TAG"
export DOCKER_BUILDKIT=1
docker build ${DOCKERFILE_FLAG} -t "$FINAL_FULL_IMAGE_TAG" --cache-from "$IMAGE:$CACHE_TAG" --build-arg "ENVIRONMENT=${ENVIRONMENT:-}" --build-arg "BUILDKITE_BUILD_NUMBER=${BUILDKITE_BUILD_NUMBER}" .

echo "Pushing Docker image: $FINAL_FULL_IMAGE_TAG"
docker push "$FINAL_FULL_IMAGE_TAG"
