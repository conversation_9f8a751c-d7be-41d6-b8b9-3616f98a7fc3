ARG BUILDKITE_BUILD_NUMBER

FROM 730011650125.dkr.ecr.ap-southeast-2.amazonaws.com/application/jetstar-hotels-ui:${BUILDKITE_BUILD_NUMBER}-base as builder


ENV LD_PRELOAD=/usr/lib/libjemalloc.so.2

USER nonroot

WORKDIR /application

ENV APPLICATION=jetstar-hotels-ui
ENV NODE_ENV=production

ENV PORT 8080
EXPOSE 8080

ARG BUILD_ID
ENV BUILD_ID=${BUILD_ID}

ARG BASE_PATH
ENV BASE_PATH=${BASE_PATH}

ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}

#Use BUILDKIT caching
RUN --mount=type=cache,id=jetstar-hotels-ui-yarn-cache,target=/tmp/jetstar-hotels-ui-yarn-cache \
    yarn install --frozen-lockfile --production --force


FROM 730011650125.dkr.ecr.ap-southeast-2.amazonaws.com/base/node:22.latest-runtime-176

ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}

WORKDIR /application

COPY --from=builder --chown=nonroot:nonroot /application/ ./
COPY --chown=nonroot:nonroot builds/${ENVIRONMENT}/ build


CMD ["src/server.js", "-p", "8080"]
