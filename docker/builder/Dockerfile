FROM 730011650125.dkr.ecr.ap-southeast-2.amazonaws.com/base/node:22.latest-builder-176

# Install app dependencies
COPY --chown=nonroot:nonroot package.json yarn.lock ./

RUN CYPRESS_INSTALL_BINARY=0 yarn install

#seems this needs to be here as yarn install may be changing ownership for builds directory??

COPY --chown=nonroot:nonroot . .

# Set build number, fallback to dev if not supplied
ARG BUILD_NUMBER=dev
ENV BUILD_NUMBER=${BUILD_NUMBER} \
    NODE_ENV=production \
    PORT=8080

EXPOSE 8080
