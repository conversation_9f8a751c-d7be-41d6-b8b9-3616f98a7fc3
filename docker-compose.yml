version: '3.4'

x-default-environment: &default-environment
  CI: 'true'
  TZ: 'Australia/Sydney'
  BUILDKITE_BRANCH:
  BUILDKITE_COMMIT:
  BUILDKITE_BUILD_NUMBER:
  BUILDBOX_AGENT_UID:
  BUILDBOX_AGENT_GID:
  BUILD_FARM_RUN:
  APPLICATION: jetstar-hotels-ui
  ENVIRONMENT:
  REPO_URL:
  REPO_NAME:
  IMAGE_TAG:
  BROWSERSTACK_USER:
  BROWSERSTACK_KEY:
  BROWSERSTACK_HOTELS_HOST:

services:
  base:
    build:
      context: .
      dockerfile: docker/builder/Dockerfile
    environment:
      <<: *default-environment
      BUILDKITE_PARALLEL_JOB:

  cypress:
    build:
      context: .
      dockerfile: ./cypress/Dockerfile
    volumes:
      - ./cypress:/application/cypress
    environment:
      CI: 'true'
      BUILDBOX_AGENT_UID:
      BUILDBOX_AGENT_GID:
      BUILDKITE_BUILD_NUMBER:
    ipc: host

  terraform:
    image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/base/terraform:${TERRAFORM_VERSION}
    environment:
      <<: *default-environment
      BUILDKITE_BUILD_NUMBER:
      BUILD_FARM_RUN:
      CLUSTER:
      REPO_URL:
      REPO_NAME:
      BUILD_TAG:
      IMAGE_TAGS:
      DEPLOY_TYPE:
      DEPLOY_TO_ENV:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS:
      DEPLOY_BUCKET:
      APP_NAME:
    volumes:
      - .:/application
    working_dir: /application
