terraform {
  backend "s3" {
    bucket = "hooroo-terraform-state"
    key    = "jetstar-hotels-ui/terraform.tfstate"
    region = "ap-southeast-2"
  }
}

module "common" {
  source = "git::ssh://**************/hooroo/terraform-aws-common.git?ref=stable"

  availability_zones = ["a", "b", "c"]
}

module "load_balancer" {
  source = "git::ssh://**************/hooroo/terraform-aws-ecs//modules/ecs_loadbalancer?ref=stable"

  application = var.application
  cluster     = "hooroo-${local.workspace["account"]}"

  load_balancer_settings    = {
    health_check_path = "/au/en/hotels/api/lb-health-check"
  }

  providers = {
    aws          = aws
    aws.services = aws.services
  }
}

module "application" {
  source      = "git::ssh://**************/hooroo/terraform-aws-ecs//stacks/ecs_service?ref=stable"
  application = var.application
  cluster     = "hooroo-${local.workspace["account"]}"

  ecs_service_settings = {
    min_count     = local.workspace["app_min_count"]
    max_count     = local.workspace["app_max_count"]
    desired_count = local.workspace["app_desired_count"]
  }

  container_definition_settings = [
    {
      image         = var.image
      image_version = var.image_version != null ? var.image_version : terraform.workspace

      cpu    = 320
      memory = 512

      secrets = [
        for secret_key in local.secret_keys : {
          name      = "${secret_key}"
          valuefrom = "${data.aws_secretsmanager_secret.app-secret.arn}:${secret_key}::"
        }
      ]

      load_balancers = [
        {
          target_group_arn = module.load_balancer.target_group_arn
        }
      ]

      environment = [
        {
          name  = "AWS_ENV_NAME"
          value = terraform.workspace
        },
        {
          name  = "DANGEROUSLY_DISABLE_HOST_CHECK"
          value = "true"
        }
      ]
    }
  ]

  providers = {
    aws          = aws
    aws.services = aws.services
  }
}

module "s3_assets_bucket" {
  source               = "git::ssh://**************/hooroo/terraform-aws-s3.git//modules/bucket?ref=stable"
  bucket_name          = local.bucket_name
  custom_bucket_policy = data.aws_iam_policy_document.s3_cloudfront_policy.json
  application          = var.application
  cors_rules = [{
    allowed_headers = null
    allowed_methods = ["GET"]
    allowed_origins = split(",", local.workspace["cors_origins"])
    expose_headers  = null
    max_age_seconds = null
  }]
}

module "s3_deploy_role" {
  source      = "git::ssh://**************/hooroo/terraform-aws-s3.git//modules/s3_deploy_role?ref=stable"
  application = var.application
  bucket_name = local.bucket_name
}

resource "aws_cloudfront_origin_access_identity" "origin_access_identity" {
  comment = local.envapp_name
}

resource "aws_cloudfront_distribution" "s3_distribution" {
  origin {
    domain_name = module.s3_assets_bucket.bucket_domain_name
    origin_id   = local.bucket_name

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }
  }

  enabled         = true
  is_ipv6_enabled = true
  comment         = local.envapp_name
  price_class     = "PriceClass_All"
  aliases         = ["${terraform.workspace}-jetstar-hotels-cdn.jetstarhotels.com"]

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = local.bucket_name

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }

      headers = ["Access-Control-Request-Headers", "Access-Control-Request-Method", "Origin"]
    }

    viewer_protocol_policy = "redirect-to-https"
    compress               = true
  }

  logging_config {
    include_cookies = false
    bucket          = terraform.workspace == "production" ? "hooroo-logs-production.s3.amazonaws.com" : "hooroo-logs-nonprod.s3.amazonaws.com"
    prefix          = "CFLogs/${local.envapp_name}"
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application
  }

  viewer_certificate {
    acm_certificate_arn      = data.aws_acm_certificate.wildcard_jetstarhotels_com.arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2019"
  }
}

module "external_dns_alias" {
  source       = "git::ssh://**************/hooroo/terraform-aws-route53.git//modules/alias?ref=stable"
  enabled      = true
  private_zone = false
  dns_name     = aws_cloudfront_distribution.s3_distribution.domain_name
  zone_id      = aws_cloudfront_distribution.s3_distribution.hosted_zone_id
  aliases      = ["${terraform.workspace}-jetstar-hotels-cdn"]
  domain_name  = "jetstarhotels.com"
  providers = {
    aws          = aws
    aws.services = aws.services
  }
}
