provider "aws" {
  region  = var.region

  assume_role {
    role_arn     = module.common.provider_role_arn
    session_name = "Terraform"
  }
}

provider "aws" {
  region  = var.region
  alias   = "services"

  assume_role {
    role_arn     = module.common.services_role_arn
    session_name = "Terraform"
  }
}

provider "aws" {
  region  = "us-east-1"
  alias   = "us_east_1"

  assume_role {
    role_arn     = module.common.provider_role_arn
    session_name = "TerraformUsEast1"
  }
}

// Get the state from the Ops infrastructure
data "terraform_remote_state" "cluster-state" {
  backend   = "s3"
  workspace = local.workspace["account"]
  config = {
    bucket = "hooroo-terraform-state"
    key    = "container-cluster/terraform.tfstate"
    region = "ap-southeast-2"
  }
}

data "aws_iam_policy_document" "s3_cloudfront_policy" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["arn:aws:s3:::${local.bucket_name}/*"]

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.origin_access_identity.iam_arn]
    }
  }

  statement {
    actions   = ["s3:GetObject"]
    resources = ["arn:aws:s3:::${local.bucket_name}/*"]

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    condition {
      test     = "StringEquals"
      variable = "aws:sourceVpc"
      values   = [module.common.vpc_id]
    }
  }
}

data "aws_acm_certificate" "wildcard_jetstarhotels_com" {
  provider    = aws.us_east_1
  domain      = "*.qantashotels.com"
  statuses    = ["ISSUED"]
  most_recent = true
}

data "aws_secretsmanager_secret" "app-secret" {
  name = "ci/${terraform.workspace}/${var.application}"
}
