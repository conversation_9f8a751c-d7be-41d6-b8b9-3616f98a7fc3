variable "region" {
  default = "ap-southeast-2"
}

variable "application" {
  description = "The name of the application"
  default     = "jetstar-hotels-ui"
}

variable "image" {
  default     = "730011650125.dkr.ecr.ap-southeast-2.amazonaws.com/application/jetstar-hotels-ui"
  description = "The image to deploy"
}

variable "image_version" {
  description = "The image version to deploy"
  default     = null
}

locals {
  bucket_name = "${terraform.workspace}-jetstar-hotels-ui"
  envapp_name = "${terraform.workspace}-jetstar-hotels-ui"
  workspace   = merge(local.defaults, local.environments[terraform.workspace])

  secret_keys = [
      "NODE_ENV",
      "HOTELS_API_BASE_PATH",
      "SSO_HOST",
      "SERVER_SENTRY_DSN",
      "PUBLIC_PATH",
      "NEW_RELIC_LICENSE_KEY",
      "SPLIT_AUTHORIZATION_KEY",
      "PBE_SPLIT_AUTHORIZATION_KEY",
      "CELEBRUS_BASE_PATH",
      "FORCE_DOMAIN",
      "NEXT_PUBLIC_ENVIRONMENT",
      "NEXT_PUBLIC_HOTELS_API_HOST",
      "NEXT_PUBLIC_HOTELS_API_BASE_PATH",
      "NEXT_PUBLIC_HOTELS_HOST",
      "NEXT_PUBLIC_PUBLIC_PATH",
      "NEXT_PUBLIC_SENTRY_DSN",
      "NEXT_PUBLIC_QFF_LOGIN_BUNDLE_URL",
      "NEXT_PUBLIC_SIMPLICITY_BUNDLE_URL",
      "NEXT_PUBLIC_LEGACY_HOTELS_HOST",
      "NEXT_PUBLIC_GTM_ID",
      "NEXT_PUBLIC_GTM_AUTH",
      "NEXT_PUBLIC_SPLIT_AUTHORIZATION_KEY",
      "NEXT_PUBLIC_GOOGLE_MAPS_STATIC_API_URL",
      "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY",
      "NEXT_PUBLIC_SPLIT_BROWSER_REFRESH_RATE_SECS",
      "NEXT_PUBLIC_SNARE_JS_URL",
      "NEXT_PUBLIC_MASTERCARD_HOST",
      "NEXT_PUBLIC_MASTERCARD_VERSION",
      "NEXT_PUBLIC_MASTERCARD_MERCHANT_ID",
      "NEXT_PUBLIC_ADYEN_API_VERSION",
      "NEXT_PUBLIC_ADYEN_MERCHANT_ID",
      "NEXT_PUBLIC_ADYEN_CLIENT_KEY",
      "NEXT_PUBLIC_ADYEN_ENVIRONMENT",
      "NEXT_PUBLIC_BOOKING_CONFIRMATION_NPS_SURVEY_URL",
      "NEXT_PUBLIC_SALESFORCE_DEPLOYMENT_ID",
      "NEXT_PUBLIC_SALESFORCE_ORG_ID",
      "NEXT_PUBLIC_SALESFORCE_LIVE_CHAT_ID",
      "NEXT_PUBLIC_RECAPTCHA_SITE_KEY",
      "NEXT_PUBLIC_LUXE_HOST",
      "NEXT_PUBLIC_HOLIDAY_PACKAGES_HOST",
      "NEXT_PUBLIC_SANITY_DATASET_NAME",
      "NEXT_PUBLIC_PREVIOUS_TIER_NAME",
      "NEXT_PUBLIC_AUTH_API_URL",
      "NEXT_PUBLIC_LSL_AUTH_URL",
      "NEXT_PUBLIC_ADYEN_QEPG_CLIENT_KEY",
      "NEXT_PUBLIC_OPTIMIZELY_DATAFILE",
  ]

}
