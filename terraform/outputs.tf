output "image_version" {
  value = var.image_version != null ? var.image_version : terraform.workspace
}

output "image" {
  value = var.image
}

output "alb_arn" {
  value = module.load_balancer.alb_arn
}

output "alb_dns_name" {
  value = module.load_balancer.alb_dns_name
}

output "alb_zone_id" {
  value = module.load_balancer.alb_zone_id
}

output "aws_alb_listener_arn" {
  value = module.load_balancer.aws_alb_listener_arn
}

output "cluster_name" {
  value = module.application.cluster_name
}

output "service_names" {
  value = module.application.service_name
}
