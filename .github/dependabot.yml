version: 2
updates:
  - package-ecosystem: 'npm'
    directory: '/'
    open-pull-requests-limit: 2
    schedule:
      interval: 'daily'
      time: '01:00'
      timezone: 'Australia/Sydney'
    groups:
      production-dependencies:
        dependency-type: 'production'
        update-types:
          - 'minor'
          - 'patch'
      development-dependencies:
        dependency-type: 'development'
    ignore:
      - dependency-name: '@next/bundle-analyzer'
        versions: ['>=12.1.0']
      - dependency-name: '@testing-library/react'
        versions: ['>=13.0.0']
      - dependency-name: '@types/node'
        versions: ['>=22.0.0']
      - dependency-name: '@typescript-eslint/eslint-plugin'
        versions: ['>=7.0.0']
      - dependency-name: '@typescript-eslint/parser'
        versions: ['>=7.0.0']
      - dependency-name: 'eslint'
        versions: ['>=9.0.0']
      - dependency-name: 'jest-emotion'
        versions: ['>=11.0.0']
      - dependency-name: 'next'
        versions: ['>=12.1.0']
      - dependency-name: 'react'
        versions: ['>=18.0.0']
      - dependency-name: 'react-dom'
        versions: ['>=18.0.0']
      - dependency-name: 'react-is'
        versions: ['>=18.0.0']
      - dependency-name: 'react-test-renderer'
        versions: ['>=18.0.0']
