{"name": "jetstar-hotels-ui", "version": "0.1.0", "private": true, "scripts": {"analyse": "source-map-explorer 'build/client/static/js/*.js'", "start": "node src/server.js", "dev": "yarn start", "build": "next build", "build:dev": "env-cmd -f .env.development yarn run build", "build:ted": "env-cmd -f .env.ted yarn run build", "test:watch": "NODE_ENV=test jest --config tests/jest/jest.config.browser.ts --watch -o", "test:browser": "NODE_ENV=test jest --config tests/jest/jest.config.browser.ts", "test:browser:ci": "bash ./scripts/dev/run-sharded-browser-unit-tests.sh $((BUILDKITE_PARALLEL_JOB +1)) 4", "test:browser:watch": "NODE_ENV=test jest --config tests/jest/jest.config.browser.ts --watch", "test:server": "NODE_ENV=test jest --config tests/jest/jest.config.server.ts", "test:shard": "NODE_ENV=test jest --config tests/jest/jest.config.browser.ts --shard=$((SHARD_INDEX + 1))/$TOTAL_SHARDS", "test:server:shard": "NODE_ENV=test jest --config tests/jest/jest.config.server.ts --shard=$((SHARD_INDEX + 1))/$TOTAL_SHARDS", "test:e2e": "env-cmd -f .env.e2e cypress run --browser chrome", "test:e2e:dev": "env-cmd -f .env.e2e cypress open --browser chrome -c baseUrl=http://localhost:3000", "test:browsers-test": "NODE_ENV=test jest tests/browserstack/index.test.js --runInBand", "serve": "NODE_ENV=production node src/server.js --config tsconfig.server.json", "lint:ci": "eslint .", "lint": "eslint . --quiet --fix", "precommit": "lint-staged"}, "browserslist": {"development": ["last 2 chrome versions", "last 2 firefox versions", "last 2 edge versions"], "production": [">1%", "last 2 versions", "Firefox ESR", "not ie 11", "not dead"]}, "dependencies": {"@adyen/adyen-web": "^5.56.0", "@emotion/core": "^10.0.17", "@emotion/styled": "^10.0.17", "@loadable/component": "^5.16.7", "@loadable/server": "^5.16.7", "@optimizely/react-sdk": "^3.2.4", "@popperjs/core": "^2.11.8", "@qantasexperiences/analytics": "1.32.0", "@qantasexperiences/analytics-old": "npm:@qantasexperiences/analytics@0.20.0", "@qga/components": "^2.4.0", "@qga/roo-ui": "^5.20.1", "@qga/sanity-components": "^1.2.3", "@radix-ui/react-navigation-menu": "1.1.4", "@reach/accordion": "0.18.0", "@reach/auto-id": "0.18.0", "@reach/disclosure": "0.18.0", "@reach/dropdown": "0.18.0", "@reach/menu-button": "0.18.0", "@reach/popover": "0.18.0", "@reach/tabs": "0.18.0", "@redux-beacon/combine-events": "^1.0.0", "@redux-beacon/google-tag-manager": "^1.0.1", "@reduxjs/toolkit": "^1.9.5", "@sanity/block-content-to-markdown": "^0.0.6", "@sanity/client": "^3.4.1", "@sentry/nextjs": "^7.120.0", "@u-wave/react-vimeo": "^0.9.12", "@u-wave/react-youtube": "^0.7.4", "apicache": "^1.6.3", "axios": "^1.9.0", "body-parser": "^1.20.3", "body-scroll-lock": "^3.1.5", "bowser": "^2.11.0", "compression": "^1.8.0", "connected-next-router": "^4.2.0", "connected-react-router": "^6.9.3", "cookie-parser": "^1.4.7", "cookies": "^0.9.1", "copy-to-clipboard": "^3.3.3", "crypto": "^1.0.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dayzed": "^3.2.3", "decimal.js": "^10.5.0", "downshift": "^8.0.0", "emotion-theming": "^10.0.27", "env-cmd": "^10.1.0", "es6-map": "^0.1.5", "express": "^4.21.2", "fecha": "^4.2.3", "focus-trap-react": "^10.2.3", "get-youtube-id": "^1.0.1", "google-map-react": "2.2.5", "history": "^5.3.0", "html-react-parser": "^5.2.5", "ignore-styles": "^5.0.1", "intersection-observer": "^0.12.2", "isbot": "^3.6.13", "js-cookie": "^3.0.5", "js-sha256": "^0.11.1", "lodash": "^4.17.21", "markdown-to-jsx": "7.5.0", "msgpack-lite": "^0.1.26", "mutationobserver-shim": "^0.3.7", "next": "^12.3.6", "next-compose-plugins": "^2.2.1", "next-images": "^1.8.5", "next-redux-wrapper": "^8.1.0", "next-transpile-modules": "^10.0.1", "normalize.css": "^8.0.1", "pino": "^6.14.0", "pino-http": "^6.6.0", "pluralize": "^8.0.0", "polished": "^4.3.1", "prop-types": "^15.7.2", "query-string": "^7.1.1", "ramda": "^0.30.1", "rc-slider": "^9.7.5", "react": "^17.0.2", "react-dom": "^17.0.2", "react-focus-within": "^2.0.2", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.57.0", "react-hook-form-persist": "^3.0.0", "react-intersection-observer": "^9.16.0", "react-masonry": "^1.0.7", "react-popper": "^2.3.0", "react-redux": "^7.1.1", "react-responsive": "^9.0.2", "react-router": "^6.17.0", "react-router-dom": "^6.17.0", "react-transition-group": "^4.4.5", "react-use": "^17.6.0", "react-useportal": "^1.0.18", "redux-beacon": "^2.1.0", "redux-logic": "^3.0.3", "redux-logic-test": "^2.0.0", "reselect": "^4.1.8", "rxjs": "^7.8.2", "source-map-support": "^0.5.13", "styled-system": "^4.2.4", "universal-cookie": "^8.0.0", "universal-cookie-express": "^8.0.0", "uuid": "^9.0.1", "visibilityjs": "^2.0.2", "wicg-inert": "^3.1.3"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/eslint-parser": "^7.27.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@cypress/webpack-preprocessor": "^6.0.2", "@loadable/webpack-plugin": "^5.15.2", "@next/bundle-analyzer": "^13.4.3", "@qga/pino-pretty-logger": "^1.0.0", "@sentry/cli": "^2.46.0", "@testing-library/cypress": "10.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "^8.0.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.17", "@types/node": "^22.15.30", "@types/react-redux": "^7.1.25", "@types/styled-system": "^4.2.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@wojtekmaj/enzyme-adapter-react-17": "^0.8.0", "ansi-colors": "^4.1.3", "assets-webpack-plugin": "^7.1.1", "babel-jest": "^30.0.0", "babel-loader": "^9.2.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-lodash": "^3.3.4", "babel-plugin-styled-components": "^2.1.4", "bundle-stats-webpack-plugin": "^4.20.2", "cheerio": "^1.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "cssnano": "^7.0.7", "cypress": "^14.4.1", "cypress-iframe": "^1.0.1", "dotenv": "^16.5.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.8", "eslint": "^8.41.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-cypress": "^4.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.13.3", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-testcafe": "^0.2.1", "esm": "^3.2.25", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "jest": "^30.0.0", "jest-canvas-mock": "^2.5.1", "jest-emotion": "^10.0.32", "jest-environment-jsdom": "^29.7.0", "jest-enzyme": "^7.1.2", "jest-fetch-mock": "^3.0.3", "jest-mock-random": "^1.1.1", "jest-watch-typeahead": "^2.2.2", "jest-when": "^3.7.0", "lint-staged": "^15.4.3", "mini-css-extract-plugin": "^2.9.2", "mockdate": "^3.0.5", "next-router-mock": "^1.0.2", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.2.3", "prettier": "^3.5.3", "react-is": "^17.0.1", "react-remock": "^0.8.2", "react-test-renderer": "^17.0.2", "redux-mock-store": "^1.5.5", "rimraf": "^6.0.1", "selenium-webdriver": "^4.33.0", "start-server-webpack-plugin": "^2.2.5", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3", "webpack": "^5.99.9"}, "lint-staged": {"*.js": "eslint"}, "husky": {"hooks": {"pre-commit": "yarn precommit"}}, "resolutions": {"**/@types/node": "^22.15.30", "react-masonry/react": "^17.0.1", "react-masonry/react-dom": "^17.0.1"}}