env:
  REPO_URL: 730011650125.dkr.ecr.ap-southeast-2.amazonaws.com
  REPO_NAME: application/jetstar-hotels-ui
  APP_NAME: jetstar-hotels-ui
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1
  BUILDKIT_PROGRESS: plain

.defaults: &defaults
  agents:
    ci-env: production

.ecr-plugin: &ecr-plugin
  ecr#v2.9.0:
    login: true
    no-include-email: true

.ecs-deploy: &ecs-deploy
  <<: *defaults
  command: hooroo-ci/scripts/ecs-deploy-ext
  branches: master
  timeout: 20
  plugins:
    docker-compose#v4.16.0:
      run: terraform
      pull: terraform
      env:
        - APPLICATION=jetstar-hotels-ui
        - ENVIRONMENT
        - VARS

.deploy-s3: &deploy-s3
  command: hooroo-ci/scripts/deploy-s3
  branches: master
  plugins:
    docker-compose#v4.16.0:
      pull: terraform
      run: terraform

.tag: &tag
  command: hooroo-ci/scripts/image-tag
  branches: master
  plugins:
    docker-compose#v4.16.0:
      run: terraform

.docker-base: &docker-base
  docker-compose#v4.16.0:
    run: base
    volumes:
      - ./builds:/application/builds
    env:
      - APP_NAME=jetstar-hotels-ui
      - ENVIRONMENT
      - SENTRY_AUTH_TOKEN
      - SENTRY_ORG=qantashotels

.create-build: &create-build
  <<: *defaults
  command: hooroo-ci/scripts/create-build
  artifact_paths:
    - builds/*.tar.gz
  plugins:
    <<: *docker-base

.image-build: &image-build
  <<: *defaults
  command: hooroo-ci/scripts/image-build
  plugins:
    <<: *ecr-plugin

.run-base: &run-base
  <<: *defaults
  plugins:
    docker-compose#v4.16.0:
      run: base

notify:
  - slack: '#team-bellhops'
    if: build.state == "failed" && build.branch == "master"

steps:
  - label: ':docker: Build Base Image (Without Artifacts)'
    <<: *defaults
    plugins:
      <<: *ecr-plugin
      docker-compose#v4.16.0:
        build: base
        image-repository: ${REPO_URL}/${REPO_NAME}
        image-name: ${BUILDKITE_BUILD_NUMBER}-base
        cache-from: base:${REPO_URL}/${REPO_NAME}:base
        args:
          - BUILD_NUMBER=${BUILDKITE_BUILD_NUMBER}
          - BUILDKIT_INLINE_CACHE=1
    artifact_paths:
      - package.json
    retry:
      automatic:
        limit: 2

  - wait

  - label: ':docker: Cache Base Image'
    <<: *defaults
    plugins:
      <<: *ecr-plugin
      docker-compose#v4.16.0:
        push: base:${REPO_URL}/${REPO_NAME}:base
    retry:
      automatic:
        limit: 2

  - label: ':eslint: Lint'
    <<: *run-base
    command: yarn run lint
    cancel_on_build_failing: true

  - label: ':jest: Test Browser'
    <<: *run-base
    command: yarn run test:browser:ci
    parallelism: 4
    concurrency_group: ${APP_NAME}-testgroup
    concurrency: 4

  - label: ':jest: Test Server'
    <<: *run-base
    command: yarn run test:server

  - label: ':hammer: Build Production'
    <<: *create-build
    branches: master
    env:
      ENVIRONMENT: production
    retry:
      automatic:
        limit: 2

  - label: ':hammer: Build Staging'
    <<: *create-build
    branches: master pr/* test/*
    env:
      ENVIRONMENT: staging
    retry:
      automatic:
        limit: 2

  - label: ':hammer: Build SIT'
    <<: *create-build
    branches: master pr/*
    env:
      ENVIRONMENT: sit
    retry:
      automatic:
        limit: 2

  - wait

  - label: ':docker: Build Staging Runtime Image'
    <<: *image-build
    branches: master pr/* test/*
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: builds/staging.tar.gz
      ENVIRONMENT: staging
      IMAGE_TAG: ${REPO_URL}/${REPO_NAME}:staging-${BUILDKITE_BUILD_NUMBER}
      RUNTIME_DOCKERFILE: docker/runtime/Dockerfile
    retry:
      automatic:
        limit: 2

  - label: ':docker: Build SIT Runtime Image'
    <<: *image-build
    branches: master pr/*
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: builds/sit.tar.gz
      ENVIRONMENT: sit
      IMAGE_TAG: ${REPO_URL}/${REPO_NAME}:sit-${BUILDKITE_BUILD_NUMBER}
      RUNTIME_DOCKERFILE: docker/runtime/Dockerfile
    retry:
      automatic:
        limit: 2

  - wait

  - label: ':docker: Build Production Runtime Image'
    <<: *image-build
    branches: master
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: builds/production.tar.gz
      ENVIRONMENT: production
      IMAGE_TAG: ${REPO_URL}/${REPO_NAME}:production-${BUILDKITE_BUILD_NUMBER}
      RUNTIME_DOCKERFILE: docker/runtime/Dockerfile
    retry:
      automatic:
        limit: 2

  - wait

  - label: ':s3: Deploy Staging to S3'
    <<: *deploy-s3
    branches: master pr/* test/*
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: builds/staging.tar.gz
      DEPLOY_TO_ENV: staging
      DEPLOY_TYPE: s3
      DEPLOY_BUCKET: s3://staging-jetstar-hotels-ui
    retry:
      automatic:
        limit: 2

  - label: ':s3: Deploy SIT to S3'
    <<: *deploy-s3
    branches: master pr/*
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: builds/sit.tar.gz
      DEPLOY_TO_ENV: sit
      DEPLOY_TYPE: s3
      DEPLOY_BUCKET: s3://sit-jetstar-hotels-ui
    retry:
      automatic:
        limit: 2

  - wait

  - label: ':ship: Deploy Staging to ECS'
    <<: *ecs-deploy
    branches: master pr/* test/*
    env:
      APPLICATION: jetstar-hotels-ui
      ENVIRONMENT: staging
      VARS: image_version=staging-${BUILDKITE_BUILD_NUMBER}
    retry:
      automatic:
        limit: 2

  - label: ':ship: Deploy SIT to ECS'
    <<: *ecs-deploy
    env:
      APPLICATION: jetstar-hotels-ui
      ENVIRONMENT: sit
      VARS: image_version=sit-${BUILDKITE_BUILD_NUMBER}
    retry:
      automatic:
        limit: 2

  - wait

  - label: ':docker: Tag Staging'
    <<: *tag
    branches: master pr/* test/*
    env:
      IMAGE_TAGS: staging
      ENVIRONMENT: staging
      BUILD_TAG: staging-$BUILDKITE_BUILD_NUMBER
    retry:
      automatic:
        limit: 2

  - label: ':docker: Tag SIT'
    <<: *tag
    branches: master pr/*
    env:
      IMAGE_TAGS: sit
      ENVIRONMENT: sit
      BUILD_TAG: sit-${BUILDKITE_BUILD_NUMBER}
    retry:
      automatic:
        limit: 2

  - trigger: jetstar-hotels-ui-smoke-tests
    label: ':chrome: Smoke Tests'
    branches: master pr/* test/*
    build:
      message: '${BUILDKITE_MESSAGE}'
      commit: '${BUILDKITE_COMMIT}'
      branch: '${BUILDKITE_BRANCH}'

  - label: ':rocket: Deploy Production to S3'
    <<: *deploy-s3
    env:
      BUILDKITE_ARTIFACT_DOWNLOAD_PATHS: builds/production.tar.gz
      DEPLOY_TO_ENV: production
      DEPLOY_TYPE: s3
      DEPLOY_BUCKET: s3://production-jetstar-hotels-ui
    retry:
      automatic:
        limit: 2

  - wait

  - label: ':rocket: Deploy Production to ECS'
    <<: *ecs-deploy
    env:
      ENVIRONMENT: production
      VARS: image_version=production-${BUILDKITE_BUILD_NUMBER}
    retry:
      automatic:
        limit: 2

  - wait

  - label: ':docker: Tag Production'
    <<: *tag
    env:
      IMAGE_TAGS: production,latest
      ENVIRONMENT: production
      BUILD_TAG: production-${BUILDKITE_BUILD_NUMBER}
    retry:
      automatic:
        limit: 2
